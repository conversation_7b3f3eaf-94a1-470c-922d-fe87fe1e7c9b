const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Transaction = sequelize.define('Transaction', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  transactionId: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    defaultValue: function() {
      return 'TXN' + Date.now() + Math.random().toString(36).substring(2, 7).toUpperCase();
    }
  },

  // User information
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  userEmail: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  userName: {
    type: DataTypes.STRING(100),
    allowNull: false
  },

  // Package information
  packageId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'packages',
      key: 'id'
    }
  },
  packageName: {
    type: DataTypes.STRING(100),
    allowNull: false
  },

  // Payment details
  amount: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {
      usd: 0,
      ksh: 0
    }
  },
  exchangeRate: {
    type: DataTypes.DECIMAL(10, 4),
    allowNull: false,
    defaultValue: 150.0000
  },

  // Payment method details
  paymentMethod: {
    type: DataTypes.ENUM('mpesa', 'card'),
    allowNull: false
  },
  paymentDetails: {
    type: DataTypes.JSONB,
    defaultValue: {
      mpesaNumber: null,
      mpesaCode: null,
      mpesaReceiptNumber: null,
      cardLast4: null,
      cardBrand: null,
      cardToken: null
    }
  },

  // Transaction status
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'),
    defaultValue: 'pending'
  },

  // Status tracking
  statusHistory: {
    type: DataTypes.JSONB,
    defaultValue: []
  },

  // Subscription details
  subscriptionDetails: {
    type: DataTypes.JSONB,
    defaultValue: {
      startDate: null,
      expiryDate: null,
      duration: 30
    }
  },

  // Additional metadata
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {
      ipAddress: null,
      userAgent: null,
      source: 'web'
    }
  },

  // Processing details
  processedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  failureReason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  refundReason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  refundedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },

  // Admin notes
  adminNotes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'transactions',
  timestamps: true,
  hooks: {
    beforeCreate: (transaction) => {
      if (!transaction.transactionId) {
        transaction.transactionId = 'TXN' + Date.now() + Math.random().toString(36).substring(2, 7).toUpperCase();
      }

      // Add to status history
      if (!transaction.statusHistory) {
        transaction.statusHistory = [];
      }
      transaction.statusHistory.push({
        status: transaction.status,
        timestamp: new Date()
      });
    }
  }
});

// Virtual for formatted amount
Transaction.prototype.getFormattedAmount = function() {
  return {
    usd: `$${this.amount.usd}`,
    ksh: `KSH ${this.amount.ksh.toLocaleString()}`
  };
};

// Static method to get transaction statistics
Transaction.getStats = async function() {
  const stats = await this.findAll({
    attributes: [
      [sequelize.fn('COUNT', sequelize.col('id')), 'totalTransactions'],
      [sequelize.fn('SUM', sequelize.literal("(amount->>'usd')::numeric")), 'totalRevenue'],
      [sequelize.fn('COUNT', sequelize.literal("CASE WHEN status = 'completed' THEN 1 END")), 'completedTransactions'],
      [sequelize.fn('COUNT', sequelize.literal("CASE WHEN status = 'pending' THEN 1 END")), 'pendingTransactions'],
      [sequelize.fn('COUNT', sequelize.literal("CASE WHEN status = 'failed' THEN 1 END")), 'failedTransactions'],
      [sequelize.fn('COUNT', sequelize.literal("CASE WHEN \"paymentMethod\" = 'mpesa' THEN 1 END")), 'mpesaTransactions'],
      [sequelize.fn('COUNT', sequelize.literal("CASE WHEN \"paymentMethod\" = 'card' THEN 1 END")), 'cardTransactions']
    ],
    raw: true
  });

  const result = stats[0] || {};
  return {
    totalTransactions: parseInt(result.totalTransactions) || 0,
    totalRevenue: parseFloat(result.totalRevenue) || 0,
    completedTransactions: parseInt(result.completedTransactions) || 0,
    pendingTransactions: parseInt(result.pendingTransactions) || 0,
    failedTransactions: parseInt(result.failedTransactions) || 0,
    mpesaTransactions: parseInt(result.mpesaTransactions) || 0,
    cardTransactions: parseInt(result.cardTransactions) || 0
  };
};

module.exports = Transaction;