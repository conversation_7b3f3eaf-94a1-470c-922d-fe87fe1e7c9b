const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  // Transaction identification
  transactionId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'TXN' + Date.now() + Math.random().toString(36).substr(2, 5).toUpperCase();
    }
  },
  
  // User information
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  userEmail: {
    type: String,
    required: true
  },
  userName: {
    type: String,
    required: true
  },
  
  // Package information
  packageId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Package',
    required: true
  },
  packageName: {
    type: String,
    required: true
  },
  
  // Payment details
  amount: {
    usd: {
      type: Number,
      required: true,
      min: 0
    },
    ksh: {
      type: Number,
      required: true,
      min: 0
    }
  },
  exchangeRate: {
    type: Number,
    required: true,
    min: 0
  },
  
  // Payment method details
  paymentMethod: {
    type: String,
    enum: ['mpesa', 'card'],
    required: true
  },
  paymentDetails: {
    // M-Pesa specific fields
    mpesaNumber: {
      type: String,
      default: null
    },
    mpesaCode: {
      type: String,
      default: null
    },
    mpesaReceiptNumber: {
      type: String,
      default: null
    },
    
    // Card specific fields
    cardLast4: {
      type: String,
      default: null
    },
    cardBrand: {
      type: String,
      default: null
    },
    cardToken: {
      type: String,
      default: null
    }
  },
  
  // Transaction status
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
    default: 'pending'
  },
  
  // Status tracking
  statusHistory: [{
    status: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded']
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    note: {
      type: String,
      default: null
    }
  }],
  
  // Subscription details
  subscriptionDetails: {
    startDate: {
      type: Date,
      default: null
    },
    expiryDate: {
      type: Date,
      default: null
    },
    duration: {
      type: Number, // in days
      default: 30
    }
  },
  
  // Additional metadata
  metadata: {
    ipAddress: {
      type: String,
      default: null
    },
    userAgent: {
      type: String,
      default: null
    },
    source: {
      type: String,
      default: 'web'
    }
  },
  
  // Processing details
  processedAt: {
    type: Date,
    default: null
  },
  failureReason: {
    type: String,
    default: null
  },
  refundReason: {
    type: String,
    default: null
  },
  refundedAt: {
    type: Date,
    default: null
  },
  
  // Admin notes
  adminNotes: {
    type: String,
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
transactionSchema.index({ transactionId: 1 });
transactionSchema.index({ userId: 1 });
transactionSchema.index({ status: 1 });
transactionSchema.index({ paymentMethod: 1 });
transactionSchema.index({ createdAt: -1 });
transactionSchema.index({ 'paymentDetails.mpesaCode': 1 });

// Virtual for formatted amount
transactionSchema.virtual('formattedAmount').get(function() {
  return {
    usd: `$${this.amount.usd}`,
    ksh: `KSH ${this.amount.ksh.toLocaleString()}`
  };
});

// Virtual for user reference
transactionSchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

// Virtual for package reference
transactionSchema.virtual('package', {
  ref: 'Package',
  localField: 'packageId',
  foreignField: '_id',
  justOne: true
});

// Pre-save middleware to add status to history
transactionSchema.pre('save', function(next) {
  if (this.isModified('status')) {
    this.statusHistory.push({
      status: this.status,
      timestamp: new Date()
    });
  }
  next();
});

// Static method to get transaction statistics
transactionSchema.statics.getStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: null,
        totalTransactions: { $sum: 1 },
        totalRevenue: { $sum: '$amount.usd' },
        completedTransactions: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        pendingTransactions: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
        },
        failedTransactions: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        },
        mpesaTransactions: {
          $sum: { $cond: [{ $eq: ['$paymentMethod', 'mpesa'] }, 1, 0] }
        },
        cardTransactions: {
          $sum: { $cond: [{ $eq: ['$paymentMethod', 'card'] }, 1, 0] }
        }
      }
    }
  ]);
  
  return stats[0] || {
    totalTransactions: 0,
    totalRevenue: 0,
    completedTransactions: 0,
    pendingTransactions: 0,
    failedTransactions: 0,
    mpesaTransactions: 0,
    cardTransactions: 0
  };
};

// Static method to get recent transactions
transactionSchema.statics.getRecent = function(limit = 10) {
  return this.find()
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('userId', 'firstName lastName email')
    .populate('packageId', 'name price');
};

const Transaction = mongoose.model('Transaction', transactionSchema);

module.exports = Transaction;
