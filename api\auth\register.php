<?php
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../config/database.php';

// Get posted data
$data = json_decode(file_get_contents("php://input"));

// Validate required fields
if (!empty($data->firstName) && !empty($data->lastName) && !empty($data->email) && !empty($data->password)) {
    
    // Initialize database
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if user already exists
    $check_query = "SELECT id FROM users WHERE email = :email LIMIT 1";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(":email", $data->email);
    $check_stmt->execute();
    
    if ($check_stmt->rowCount() > 0) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "User with this email already exists"
        ]);
        exit();
    }
    
    // Validate email format
    if (!filter_var($data->email, FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Invalid email format"
        ]);
        exit();
    }
    
    // Validate password length
    if (strlen($data->password) < 6) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Password must be at least 6 characters long"
        ]);
        exit();
    }
    
    // Prepare insert query
    $query = "INSERT INTO users 
              (first_name, last_name, email, password, phone, telegram_username, role, is_active) 
              VALUES 
              (:first_name, :last_name, :email, :password, :phone, :telegram_username, 'user', 1)";
    
    $stmt = $db->prepare($query);
    
    // Hash password
    $hashed_password = password_hash($data->password, PASSWORD_DEFAULT);
    
    // Process telegram username
    $telegram_username = null;
    if (!empty($data->telegramUsername)) {
        $telegram_username = $data->telegramUsername;
        if (!str_starts_with($telegram_username, '@')) {
            $telegram_username = '@' . $telegram_username;
        }
    }
    
    // Bind parameters
    $stmt->bindParam(":first_name", $data->firstName);
    $stmt->bindParam(":last_name", $data->lastName);
    $stmt->bindParam(":email", $data->email);
    $stmt->bindParam(":password", $hashed_password);
    $stmt->bindParam(":phone", $data->phone);
    $stmt->bindParam(":telegram_username", $telegram_username);
    
    // Execute query
    if ($stmt->execute()) {
        
        // Get the created user ID
        $user_id = $db->lastInsertId();
        
        // Generate JWT token
        $token = base64_encode(json_encode([
            'user_id' => $user_id,
            'email' => $data->email,
            'role' => 'user',
            'exp' => time() + (7 * 24 * 60 * 60) // 7 days
        ]));
        
        // Prepare user data for response
        $user_data = [
            'id' => $user_id,
            'first_name' => $data->firstName,
            'last_name' => $data->lastName,
            'email' => $data->email,
            'phone' => $data->phone,
            'telegram_username' => $telegram_username,
            'telegram_status' => 'pending',
            'role' => 'user',
            'is_active' => true,
            'subscription' => null
        ];
        
        // Return success response
        http_response_code(201);
        echo json_encode([
            "success" => true,
            "message" => "User registered successfully",
            "token" => $token,
            "user" => $user_data
        ]);
        
    } else {
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Unable to register user"
        ]);
    }
    
} else {
    // Missing required fields
    http_response_code(400);
    echo json_encode([
        "success" => false,
        "message" => "First name, last name, email and password are required"
    ]);
}
?>
