# ForexClass - React + PHP + MySQL Setup Instructions

## 🎯 Project Overview

Your ForexClass project has been restructured to use:
- **Frontend**: React.js with Tailwind CSS
- **Backend**: PHP with MySQL database
- **Database**: MySQL with phpMyAdmin
- **Authentication**: PHP-based login/register system

## 📋 Prerequisites

### 1. Install XAMPP (Recommended)
Download and install XAMPP from: https://www.apachefriends.org/
- Includes Apache, MySQL, PHP, and phpMyAdmin
- Easy setup for local development

### 2. Install Node.js
Download from: https://nodejs.org/
- Required for React development

## 🚀 Setup Instructions

### Step 1: Setup XAMPP

1. **Install XAMPP** and start the following services:
   - ✅ Apache (for PHP)
   - ✅ MySQL (for database)

2. **Access phpMyAdmin**:
   - Open browser: http://localhost/phpmyadmin
   - Default username: `root`
   - Default password: (leave empty)

### Step 2: Create Database

1. **Import Database Schema**:
   - In phpMyAdmin, click "Import"
   - Select the file: `database/forexclass.sql`
   - Click "Go" to import

2. **Verify Database**:
   - Database name: `forexclass`
   - Tables created: `users`, `packages`, `user_subscriptions`, `transactions`
   - Sample data imported automatically

### Step 3: Setup PHP Backend

1. **Copy API files to XAMPP**:
   ```
   Copy the 'api' folder to: C:/xampp/htdocs/forexclass/
   ```

2. **Folder structure should be**:
   ```
   C:/xampp/htdocs/forexclass/
   ├── api/
   │   ├── config/
   │   │   └── database.php
   │   ├── auth/
   │   │   ├── login.php
   │   │   └── register.php
   │   └── packages/
   │       └── index.php
   ```

3. **Test API endpoints**:
   - Health check: http://localhost/forexclass/api/config/database.php
   - Packages: http://localhost/forexclass/api/packages/index.php

### Step 4: Setup React Frontend

1. **Navigate to frontend directory**:
   ```bash
   cd frontend-react
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Install additional packages**:
   ```bash
   npm install axios react-hot-toast lucide-react
   npm install -D tailwindcss postcss autoprefixer
   npx tailwindcss init -p
   ```

4. **Start React development server**:
   ```bash
   npm start
   ```

5. **Access React app**:
   - URL: http://localhost:3000

## 🔧 Configuration

### Database Configuration (api/config/database.php)
```php
private $host = "localhost";
private $db_name = "forexclass";
private $username = "root";
private $password = "";
```

### React API Configuration
The React app is configured to make API calls to:
```
http://localhost/forexclass/api/
```

## 🔑 Login Credentials

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123

### Sample User Accounts
- **Email**: <EMAIL> | **Password**: password123
- **Email**: <EMAIL> | **Password**: password123
- **Email**: <EMAIL> | **Password**: password123

## 📊 Database Schema

### Users Table
- `id` (Primary Key)
- `first_name`, `last_name`, `email`, `password`
- `phone`, `telegram_username`, `telegram_status`
- `role` (user/admin), `is_active`
- `last_login`, `created_at`, `updated_at`

### Packages Table
- `id` (Primary Key)
- `name`, `description`, `price`, `duration_days`
- `features` (JSON), `is_active`, `is_popular`
- `color`, `priority`, `telegram_access`

### User Subscriptions Table
- `id` (Primary Key)
- `user_id` (Foreign Key), `package_name`
- `status`, `start_date`, `expiry_date`
- `amount`, `payment_method`, `telegram_access`

### Transactions Table
- `id` (Primary Key)
- `transaction_id`, `user_id`, `package_id`
- `amount_usd`, `amount_ksh`, `exchange_rate`
- `payment_method`, `payment_details` (JSON)
- `status`, `processed_at`

## 🔗 API Endpoints

### Authentication
- **POST** `/api/auth/login.php` - User login
- **POST** `/api/auth/register.php` - User registration

### Packages
- **GET** `/api/packages/index.php` - Get all packages

### Future Endpoints (to be created)
- **GET** `/api/users/profile.php` - Get user profile
- **PUT** `/api/users/profile.php` - Update user profile
- **POST** `/api/transactions/create.php` - Create transaction
- **GET** `/api/dashboard/stats.php` - Dashboard statistics

## 🎨 Frontend Features

### Implemented Components
- ✅ **AuthContext** - Authentication state management
- ✅ **LoginForm** - User login with validation
- ✅ **RegisterForm** - User registration with validation
- ✅ **Responsive Design** - Mobile-friendly interface

### To Be Implemented
- 🔄 **Dashboard** - User dashboard
- 🔄 **Packages Display** - Package listing and selection
- 🔄 **Payment Integration** - M-Pesa and card payments
- 🔄 **Admin Panel** - Admin dashboard
- 🔄 **Profile Management** - User profile editing

## 🛠️ Development Workflow

### 1. Backend Development (PHP)
- Edit files in: `C:/xampp/htdocs/forexclass/api/`
- Test endpoints in browser or Postman
- Check database changes in phpMyAdmin

### 2. Frontend Development (React)
- Edit files in: `frontend-react/src/`
- Changes auto-reload at: http://localhost:3000
- Use browser dev tools for debugging

### 3. Database Management
- Access phpMyAdmin: http://localhost/phpmyadmin
- View/edit data directly in tables
- Export/import database as needed

## 🔍 Troubleshooting

### Common Issues

#### 1. XAMPP Services Not Starting
- Check if ports 80 (Apache) and 3306 (MySQL) are free
- Stop other web servers (IIS, etc.)
- Run XAMPP as administrator

#### 2. Database Connection Failed
- Verify MySQL service is running in XAMPP
- Check database credentials in `api/config/database.php`
- Ensure database `forexclass` exists

#### 3. CORS Errors in React
- Verify API endpoints have CORS headers
- Check React proxy configuration in `package.json`

#### 4. React App Not Loading
- Ensure Node.js is installed
- Run `npm install` in frontend-react directory
- Check for port conflicts (default: 3000)

## 📈 Next Steps

1. **Complete React Components**:
   - Dashboard layout
   - Package display cards
   - Payment modals

2. **Expand PHP API**:
   - User profile endpoints
   - Transaction processing
   - Admin management

3. **Add Features**:
   - Email notifications
   - Telegram integration
   - Payment gateways

4. **Security Enhancements**:
   - JWT token implementation
   - Input validation
   - SQL injection protection

## 🎉 Success Indicators

✅ **XAMPP running** (Apache + MySQL)
✅ **Database imported** (forexclass with sample data)
✅ **PHP API responding** (login/register working)
✅ **React app running** (http://localhost:3000)
✅ **Authentication working** (login with demo credentials)

---

**🚀 Your ForexClass project is now running on React + PHP + MySQL!**

The authentication system is fully functional with the database. You can now login/register users and all data is stored in MySQL which you can manage through phpMyAdmin.
