const express = require('express');
const TransactionService = require('../services/transactionService');
const auth = require('../middleware/auth');
const adminAuth = require('../middleware/adminAuth');
const router = express.Router();

// @route   POST /api/transactions
// @desc    Create a new transaction
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const {
      packageId,
      paymentMethod,
      paymentDetails,
      amount,
      exchangeRate,
      metadata
    } = req.body;

    // Validate required fields
    if (!packageId || !paymentMethod) {
      return res.status(400).json({
        success: false,
        message: 'Package ID and payment method are required'
      });
    }

    const transactionData = {
      userId: req.user.id,
      packageId,
      paymentMethod,
      paymentDetails,
      amount,
      exchangeRate,
      metadata: {
        ...metadata,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        source: 'web'
      }
    };

    const transaction = await TransactionService.createTransaction(transactionData);

    res.status(201).json({
      success: true,
      message: 'Transaction created successfully',
      data: { transaction }
    });

  } catch (error) {
    console.error('Create transaction error:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @route   GET /api/transactions/:transactionId
// @desc    Get transaction by ID
// @access  Private
router.get('/:transactionId', auth, async (req, res) => {
  try {
    const { transactionId } = req.params;
    const transaction = await TransactionService.getTransaction(transactionId);

    // Check if user owns this transaction or is admin
    if (transaction.userId.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: { transaction }
    });

  } catch (error) {
    console.error('Get transaction error:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @route   GET /api/transactions/user/:userId
// @desc    Get user transactions
// @access  Private
router.get('/user/:userId', auth, async (req, res) => {
  try {
    const { userId } = req.params;
    const { page, limit, status } = req.query;

    // Check if user is accessing their own transactions or is admin
    if (userId !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const result = await TransactionService.getUserTransactions(userId, {
      page: parseInt(page) || 1,
      limit: parseInt(limit) || 10,
      status
    });

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Get user transactions error:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @route   PUT /api/transactions/:transactionId/status
// @desc    Update transaction status (Admin only)
// @access  Private/Admin
router.put('/:transactionId/status', auth, adminAuth, async (req, res) => {
  try {
    const { transactionId } = req.params;
    const { status, updateData } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: 'Status is required'
      });
    }

    const transaction = await TransactionService.updateTransactionStatus(
      transactionId,
      status,
      updateData
    );

    res.json({
      success: true,
      message: 'Transaction status updated successfully',
      data: { transaction }
    });

  } catch (error) {
    console.error('Update transaction status error:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @route   POST /api/transactions/:transactionId/mpesa-callback
// @desc    Handle M-Pesa payment callback
// @access  Public (called by M-Pesa)
router.post('/:transactionId/mpesa-callback', async (req, res) => {
  try {
    const { transactionId } = req.params;
    const mpesaData = req.body;

    // Log the callback for debugging
    console.log('M-Pesa callback received:', {
      transactionId,
      data: mpesaData
    });

    const transaction = await TransactionService.processMpesaPayment(
      transactionId,
      mpesaData
    );

    res.json({
      success: true,
      message: 'M-Pesa callback processed successfully',
      data: { transaction }
    });

  } catch (error) {
    console.error('M-Pesa callback error:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @route   POST /api/transactions/:transactionId/card-callback
// @desc    Handle card payment callback
// @access  Public (called by payment processor)
router.post('/:transactionId/card-callback', async (req, res) => {
  try {
    const { transactionId } = req.params;
    const cardData = req.body;

    // Log the callback for debugging
    console.log('Card payment callback received:', {
      transactionId,
      data: cardData
    });

    const transaction = await TransactionService.processCardPayment(
      transactionId,
      cardData
    );

    res.json({
      success: true,
      message: 'Card payment callback processed successfully',
      data: { transaction }
    });

  } catch (error) {
    console.error('Card payment callback error:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @route   GET /api/transactions/stats/overview
// @desc    Get transaction statistics (Admin only)
// @access  Private/Admin
router.get('/stats/overview', auth, adminAuth, async (req, res) => {
  try {
    const { startDate, endDate, paymentMethod, status } = req.query;

    const stats = await TransactionService.getTransactionStats({
      startDate,
      endDate,
      paymentMethod,
      status
    });

    res.json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    console.error('Get transaction stats error:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @route   POST /api/transactions/expire-pending
// @desc    Expire old pending transactions (Admin only)
// @access  Private/Admin
router.post('/expire-pending', auth, adminAuth, async (req, res) => {
  try {
    const { hoursOld } = req.body;

    const expiredCount = await TransactionService.expirePendingTransactions(
      hoursOld || 24
    );

    res.json({
      success: true,
      message: `${expiredCount} transactions expired successfully`,
      data: { expiredCount }
    });

  } catch (error) {
    console.error('Expire transactions error:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
