const { sequelize } = require('../config/database');
const User = require('./User');
const Package = require('./Package');
const Transaction = require('./Transaction');

// Define associations
User.hasMany(Transaction, {
  foreignKey: 'userId',
  as: 'transactions'
});

Transaction.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

Package.hasMany(Transaction, {
  foreignKey: 'packageId',
  as: 'transactions'
});

Transaction.belongsTo(Package, {
  foreignKey: 'packageId',
  as: 'package'
});

// Export all models
module.exports = {
  sequelize,
  User,
  Package,
  Transaction
};
