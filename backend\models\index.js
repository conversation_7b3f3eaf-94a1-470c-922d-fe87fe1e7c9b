const { sequelize } = require('../config/database');
const User = require('./User');
const Package = require('./Package');

// Define associations
User.hasMany(sequelize.models.Transaction, {
  foreignKey: 'userId',
  as: 'transactions'
});

Package.hasMany(sequelize.models.Transaction, {
  foreignKey: 'packageId',
  as: 'transactions'
});

// Export all models
module.exports = {
  sequelize,
  User,
  Package,
  Transaction: sequelize.models.Transaction
};
