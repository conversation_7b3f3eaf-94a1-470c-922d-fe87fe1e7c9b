const { sequelize, testConnection } = require('./config/database');
require('dotenv').config();

console.log('🗑️  Database Reset Script');
console.log('========================');
console.log('⚠️  WARNING: This will DELETE ALL DATA in the database!');
console.log('');

async function resetDatabase() {
  try {
    // Test connection
    console.log('📡 Testing PostgreSQL connection...');
    const connected = await testConnection();
    
    if (!connected) {
      console.log('❌ Database connection failed!');
      process.exit(1);
    }

    // Confirm reset
    console.log('🔄 Resetting database...');
    console.log('   - Dropping all tables');
    console.log('   - Recreating schema');
    console.log('   - All data will be lost');
    console.log('');

    // Force sync (drops and recreates all tables)
    await sequelize.sync({ force: true });

    console.log('✅ Database reset completed!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('1. Run: npm run seed-data (to populate with sample data)');
    console.log('2. Run: npm run dev (to start the server)');
    console.log('');

  } catch (error) {
    console.error('❌ Reset failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run reset
resetDatabase();
