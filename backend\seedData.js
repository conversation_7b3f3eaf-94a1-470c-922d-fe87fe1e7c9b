const { sequelize, testConnection } = require('./config/database');
const { User, Package, Transaction } = require('./models');
require('dotenv').config();

console.log('🌱 Starting Database Seeding...');
console.log('================================');

async function seedDatabase() {
  try {
    // Test connection
    console.log('📡 Testing PostgreSQL connection...');
    const connected = await testConnection();
    
    if (!connected) {
      console.log('❌ Database connection failed!');
      process.exit(1);
    }

    // Sync database
    console.log('🔄 Synchronizing database...');
    await sequelize.sync({ force: false });

    // Check if data already exists
    const userCount = await User.count();
    const packageCount = await Package.count();

    if (userCount > 0 || packageCount > 0) {
      console.log('📊 Database already contains data:');
      console.log(`   Users: ${userCount}`);
      console.log(`   Packages: ${packageCount}`);
      console.log('');
      console.log('⚠️  Skipping seed data creation to avoid duplicates.');
      console.log('💡 To reset and reseed, run: npm run reset-db');
      process.exit(0);
    }

    // Create sample packages
    console.log('📦 Creating sample packages...');
    const packages = await Package.bulkCreate([
      {
        name: 'Basic Trader',
        description: 'Perfect for beginners starting their trading journey',
        price: 29.99,
        duration: 30,
        features: [
          'Basic trading signals',
          'Weekly market analysis',
          'Email support',
          'Trading basics course'
        ],
        isActive: true,
        popular: false,
        color: 'blue',
        priority: 1,
        telegramAccess: true
      },
      {
        name: 'Pro Trader',
        description: 'Advanced tools and signals for serious traders',
        price: 79.99,
        duration: 30,
        features: [
          'Premium trading signals',
          'Daily market analysis',
          'Live trading sessions',
          'Advanced strategies course',
          'Priority support',
          'Risk management tools'
        ],
        isActive: true,
        popular: true,
        color: 'green',
        priority: 2,
        telegramAccess: true
      },
      {
        name: 'Elite Trader',
        description: 'Complete trading mastery with personal mentoring',
        price: 149.99,
        duration: 30,
        features: [
          'VIP trading signals',
          'Real-time market alerts',
          'One-on-one mentoring',
          'Complete trading course',
          '24/7 priority support',
          'Custom trading strategies',
          'Portfolio management',
          'Exclusive webinars'
        ],
        isActive: true,
        popular: false,
        color: 'purple',
        priority: 3,
        telegramAccess: true
      }
    ]);

    console.log(`✅ Created ${packages.length} packages`);

    // Create admin user
    console.log('👤 Creating admin user...');
    const adminUser = await User.create({
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'admin123',
      phone: '+254700000000',
      telegramUsername: '@forexclassadmin',
      role: 'admin',
      isActive: true,
      subscription: {
        plan: 'Elite Trader',
        status: 'active',
        startDate: new Date(),
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
        amount: 149.99,
        paymentMethod: 'admin',
        telegramAccess: true
      }
    });

    console.log('✅ Created admin user');

    // Create sample users
    console.log('👥 Creating sample users...');
    const users = await User.bulkCreate([
      {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000001',
        telegramUsername: '@johndoe',
        role: 'user',
        subscription: {
          plan: 'Pro Trader',
          status: 'active',
          startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
          expiryDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
          amount: 79.99,
          paymentMethod: 'mpesa',
          telegramAccess: true
        }
      },
      {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000002',
        telegramUsername: '@janesmith',
        role: 'user',
        subscription: {
          plan: 'Basic Trader',
          status: 'active',
          startDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
          expiryDate: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000), // 20 days from now
          amount: 29.99,
          paymentMethod: 'card',
          telegramAccess: true
        }
      },
      {
        firstName: 'Mike',
        lastName: 'Johnson',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000003',
        role: 'user',
        subscription: {
          plan: null,
          status: 'inactive',
          startDate: null,
          expiryDate: null,
          amount: null,
          paymentMethod: null,
          telegramAccess: false
        }
      }
    ]);

    console.log(`✅ Created ${users.length} sample users`);

    // Create sample transactions
    console.log('💳 Creating sample transactions...');
    const transactions = await Transaction.bulkCreate([
      {
        transactionId: 'TXN' + Date.now() + 'SEED1',
        userId: users[0].id,
        userEmail: users[0].email,
        userName: users[0].getFullName(),
        packageId: packages[1].id, // Pro Trader
        packageName: packages[1].name,
        amount: { usd: 79.99, ksh: 11998.50 },
        exchangeRate: 150.0000,
        paymentMethod: 'mpesa',
        paymentDetails: {
          mpesaNumber: '+254700000001',
          mpesaCode: 'ABC123DEF',
          mpesaReceiptNumber: 'QGH456IJK'
        },
        status: 'completed',
        subscriptionDetails: {
          startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
          expiryDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
          duration: 30
        },
        processedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
      },
      {
        transactionId: 'TXN' + Date.now() + 'SEED2',
        userId: users[1].id,
        userEmail: users[1].email,
        userName: users[1].getFullName(),
        packageId: packages[0].id, // Basic Trader
        packageName: packages[0].name,
        amount: { usd: 29.99, ksh: 4498.50 },
        exchangeRate: 150.0000,
        paymentMethod: 'card',
        paymentDetails: {
          cardLast4: '4242',
          cardBrand: 'visa'
        },
        status: 'completed',
        subscriptionDetails: {
          startDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
          expiryDate: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000),
          duration: 30
        },
        processedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)
      }
    ]);

    console.log(`✅ Created ${transactions.length} sample transactions`);

    // Summary
    console.log('');
    console.log('🎉 Database seeding completed successfully!');
    console.log('');
    console.log('📊 Summary:');
    console.log(`   ✅ Packages: ${packages.length}`);
    console.log(`   ✅ Users: ${users.length + 1} (including admin)`);
    console.log(`   ✅ Transactions: ${transactions.length}`);
    console.log('');
    console.log('🔑 Login Credentials:');
    console.log('   Admin: <EMAIL> / admin123');
    console.log('   User 1: <EMAIL> / password123');
    console.log('   User 2: <EMAIL> / password123');
    console.log('   User 3: <EMAIL> / password123');
    console.log('');
    console.log('🚀 Ready to start the server: npm run dev');

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run seeding
seedDatabase();
