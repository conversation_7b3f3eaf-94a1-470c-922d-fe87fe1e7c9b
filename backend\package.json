{"name": "forexclass-backend", "version": "1.0.0", "description": "ForexClass Trading Platform Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "migrate": "node migrate-to-postgresql.js", "seed-data": "node seedData.js", "reset-db": "node resetDatabase.js"}, "keywords": ["forex", "trading", "education", "api", "postgresql", "express"], "author": "ForexClass Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.0", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}