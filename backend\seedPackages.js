const mongoose = require('mongoose');
const Package = require('./models/Package');
require('dotenv').config();

const seedPackages = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/forexclass');

    console.log('✅ Connected to MongoDB');

    // Clear existing packages
    await Package.deleteMany({});
    console.log('🗑️ Cleared existing packages');

    // Create default packages
    const packages = [
      {
        name: 'Basic',
        description: 'Perfect for beginners starting their forex journey',
        price: 29,
        duration: 30,
        features: [
          'Basic forex courses',
          'Email support',
          'Community access',
          'Weekly market updates',
          'Basic trading tools'
        ],
        isActive: true,
        popular: false,
        color: 'blue',
        telegramAccess: false,
        priority: 1
      },
      {
        name: 'Premium',
        description: 'Advanced features for serious traders',
        price: 99,
        duration: 30,
        features: [
          'All Basic features',
          'Advanced trading courses',
          'Telegram signals access',
          '1-on-1 mentoring sessions',
          'Priority support',
          'Live trading sessions',
          'Risk management tools'
        ],
        isActive: true,
        popular: true,
        color: 'green',
        telegramAccess: true,
        priority: 2
      },
      {
        name: 'VIP',
        description: 'Ultimate package for professional traders',
        price: 199,
        duration: 30,
        features: [
          'Everything in Premium',
          'Personal trading coach',
          'Custom trading strategies',
          'Weekly 1-on-1 sessions',
          'Exclusive VIP signals',
          'Portfolio analysis',
          'Direct phone support',
          'Trading psychology sessions'
        ],
        isActive: true,
        popular: false,
        color: 'purple',
        telegramAccess: true,
        priority: 3
      }
    ];

    // Insert packages
    const createdPackages = await Package.insertMany(packages);
    console.log(`✅ Created ${createdPackages.length} packages:`);
    
    createdPackages.forEach(pkg => {
      console.log(`   - ${pkg.name}: $${pkg.price}/month (${pkg.duration} days)`);
    });

    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding packages:', error);
    process.exit(1);
  }
};

seedPackages();
