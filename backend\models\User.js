const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  firstName: {
    type: DataTypes.STRING(50),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 50]
    }
  },
  lastName: {
    type: DataTypes.STRING(50),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 50]
    }
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true,
      notEmpty: true
    }
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [6, 255]
    }
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  telegramUsername: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  telegramStatus: {
    type: DataTypes.ENUM('pending', 'added', 'removed'),
    defaultValue: 'pending'
  },
  role: {
    type: DataTypes.ENUM('user', 'admin'),
    defaultValue: 'user'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  lastLogin: {
    type: DataTypes.DATE,
    allowNull: true
  },
  // Subscription details as JSON
  subscription: {
    type: DataTypes.JSONB,
    defaultValue: {
      plan: null,
      status: 'inactive',
      startDate: null,
      expiryDate: null,
      amount: null,
      paymentMethod: null,
      telegramAccess: false
    }
  },
  // Profile information
  profile: {
    type: DataTypes.JSONB,
    defaultValue: {
      avatar: null,
      bio: null,
      country: null,
      timezone: null,
      language: 'en'
    }
  },
  // Metadata
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {
      registrationIp: null,
      lastLoginIp: null,
      userAgent: null,
      source: 'web'
    }
  }
}, {
  tableName: 'users',
  timestamps: true,
  hooks: {
    beforeCreate: async (user) => {
      if (user.password) {
        const salt = await bcrypt.genSalt(12);
        user.password = await bcrypt.hash(user.password, salt);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password')) {
        const salt = await bcrypt.genSalt(12);
        user.password = await bcrypt.hash(user.password, salt);
      }
    }
  }
});

// Instance methods
User.prototype.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

User.prototype.getFullName = function() {
  return `${this.firstName} ${this.lastName}`;
};

User.prototype.isSubscriptionActive = function() {
  if (!this.subscription || this.subscription.status !== 'active') {
    return false;
  }
  
  if (!this.subscription.expiryDate) {
    return false;
  }
  
  return new Date(this.subscription.expiryDate) > new Date();
};

User.prototype.updateLastLogin = async function(ipAddress = null) {
  this.lastLogin = new Date();
  if (ipAddress) {
    this.metadata = {
      ...this.metadata,
      lastLoginIp: ipAddress
    };
  }
  await this.save();
};

module.exports = User;
