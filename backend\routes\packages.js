const express = require('express');
const { body, validationResult } = require('express-validator');
const Package = require('../models/Package');
const User = require('../models/User');
const auth = require('../middleware/auth');
const adminAuth = require('../middleware/adminAuth');
const router = express.Router();

// @route   GET /api/packages
// @desc    Get all packages
// @access  Public
router.get('/', async (req, res) => {
  try {
    const packages = await Package.find().sort({ priority: -1, createdAt: 1 });

    // Get subscriber count for each package
    const packagesWithStats = await Promise.all(
      packages.map(async (pkg) => {
        const subscriberCount = await User.countDocuments({
          'subscription.plan': pkg.name,
          'subscription.status': 'active'
        });

        return {
          ...pkg.toObject(),
          subscribers: subscriberCount
        };
      })
    );

    res.json({
      success: true,
      data: {
        packages: packagesWithStats
      }
    });

  } catch (error) {
    console.error('Get packages error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/packages/:id
// @desc    Get package by ID
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const package = await Package.findById(req.params.id);

    if (!package) {
      return res.status(404).json({
        success: false,
        message: 'Package not found'
      });
    }

    // Get subscriber count
    const subscriberCount = await User.countDocuments({
      'subscription.plan': package.name,
      'subscription.status': 'active'
    });

    res.json({
      success: true,
      data: {
        package: {
          ...package.toObject(),
          subscribers: subscriberCount
        }
      }
    });

  } catch (error) {
    console.error('Get package error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/packages
// @desc    Create new package (Admin only)
// @access  Private/Admin
router.post('/', auth, adminAuth, [
  body('name').trim().isLength({ min: 2, max: 100 }),
  body('price').isNumeric().isFloat({ min: 0 }),
  body('duration').isInt({ min: 1 }),
  body('description').optional().trim().isLength({ max: 500 }),
  body('features').isArray(),
  body('isActive').optional().isBoolean(),
  body('popular').optional().isBoolean(),
  body('color').optional().isIn(['blue', 'green', 'purple', 'red', 'yellow', 'indigo'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, description, price, duration, features, isActive, popular, color, telegramAccess, priority } = req.body;

    // Check if package with same name already exists
    const existingPackage = await Package.findOne({ name });
    if (existingPackage) {
      return res.status(400).json({
        success: false,
        message: 'Package with this name already exists'
      });
    }

    const package = new Package({
      name,
      description,
      price,
      duration,
      features: features.filter(f => f.trim()),
      isActive: isActive !== undefined ? isActive : true,
      popular: popular || false,
      color: color || 'blue',
      telegramAccess: telegramAccess || false,
      priority: priority || 0
    });

    await package.save();

    res.status(201).json({
      success: true,
      message: 'Package created successfully',
      data: {
        package: {
          ...package.toObject(),
          subscribers: 0
        }
      }
    });

  } catch (error) {
    console.error('Create package error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/packages/:id
// @desc    Update package
// @access  Private/Admin
router.put('/:id', auth, adminAuth, [
  body('name').optional().trim().isLength({ min: 2, max: 100 }),
  body('price').optional().isNumeric().isFloat({ min: 0 }),
  body('duration').optional().isInt({ min: 1 }),
  body('description').optional().trim().isLength({ max: 500 }),
  body('features').optional().isArray(),
  body('isActive').optional().isBoolean(),
  body('popular').optional().isBoolean(),
  body('color').optional().isIn(['blue', 'green', 'purple', 'red', 'yellow', 'indigo'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const package = await Package.findById(req.params.id);
    if (!package) {
      return res.status(404).json({
        success: false,
        message: 'Package not found'
      });
    }

    const { name, description, price, duration, features, isActive, popular, color, telegramAccess, priority } = req.body;

    // Check if name is being changed and if it conflicts with existing package
    if (name && name !== package.name) {
      const existingPackage = await Package.findOne({ name, _id: { $ne: req.params.id } });
      if (existingPackage) {
        return res.status(400).json({
          success: false,
          message: 'Package with this name already exists'
        });
      }
    }

    // Update fields
    if (name) package.name = name;
    if (description !== undefined) package.description = description;
    if (price !== undefined) package.price = price;
    if (duration !== undefined) package.duration = duration;
    if (features) package.features = features.filter(f => f.trim());
    if (isActive !== undefined) package.isActive = isActive;
    if (popular !== undefined) package.popular = popular;
    if (color) package.color = color;
    if (telegramAccess !== undefined) package.telegramAccess = telegramAccess;
    if (priority !== undefined) package.priority = priority;

    await package.save();

    // Get subscriber count
    const subscriberCount = await User.countDocuments({
      'subscription.plan': package.name,
      'subscription.status': 'active'
    });

    res.json({
      success: true,
      message: 'Package updated successfully',
      data: {
        package: {
          ...package.toObject(),
          subscribers: subscriberCount
        }
      }
    });

  } catch (error) {
    console.error('Update package error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/packages/:id
// @desc    Delete package
// @access  Private/Admin
router.delete('/:id', auth, adminAuth, async (req, res) => {
  try {
    const package = await Package.findById(req.params.id);
    if (!package) {
      return res.status(404).json({
        success: false,
        message: 'Package not found'
      });
    }

    // Check if package has active subscribers
    const activeSubscribers = await User.countDocuments({
      'subscription.plan': package.name,
      'subscription.status': 'active'
    });

    if (activeSubscribers > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete package with ${activeSubscribers} active subscribers. Please deactivate the package instead.`
      });
    }

    await Package.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Package deleted successfully'
    });

  } catch (error) {
    console.error('Delete package error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
