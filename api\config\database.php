<?php
// Database configuration for MySQL
class Database {
    private $host = "localhost";
    private $db_name = "forexclass";
    private $username = "root";
    private $password = "";
    private $conn;

    // Get database connection
    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }

        return $this->conn;
    }

    // Test database connection
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            if ($conn) {
                return array(
                    "success" => true,
                    "message" => "Database connection successful",
                    "database" => "MySQL",
                    "host" => $this->host,
                    "database_name" => $this->db_name
                );
            }
        } catch(Exception $e) {
            return array(
                "success" => false,
                "message" => "Database connection failed",
                "error" => $e->getMessage()
            );
        }
    }
}
?>
