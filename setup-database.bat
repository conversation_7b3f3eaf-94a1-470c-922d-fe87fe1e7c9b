@echo off
echo ========================================
echo   ForexClass Database Setup Script
echo ========================================
echo.

echo Checking XAMPP MySQL connection...
echo.

REM Check if XAMPP MySQL is running
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ MySQL is running
) else (
    echo ❌ MySQL is not running
    echo Please start MySQL in XAMPP Control Panel first
    pause
    exit /b 1
)

echo.
echo Creating ForexClass database...
echo.

REM Create the database using MySQL command line
C:\xampp\mysql\bin\mysql.exe -u root -e "CREATE DATABASE IF NOT EXISTS forexclass;"

if %ERRORLEVEL% EQU 0 (
    echo ✅ Database 'forexclass' created successfully
) else (
    echo ❌ Failed to create database
    pause
    exit /b 1
)

echo.
echo Importing database schema...
echo.

REM Import the SQL file
C:\xampp\mysql\bin\mysql.exe -u root forexclass < database\forexclass.sql

if %ERRORLEVEL% EQU 0 (
    echo ✅ Database schema imported successfully
) else (
    echo ❌ Failed to import schema
    pause
    exit /b 1
)

echo.
echo Verifying database setup...
echo.

REM Check if tables were created
C:\xampp\mysql\bin\mysql.exe -u root -e "USE forexclass; SHOW TABLES;"

echo.
echo ========================================
echo   Database Setup Complete! 🎉
echo ========================================
echo.
echo Database: forexclass
echo Tables: users, packages, user_subscriptions, transactions
echo Sample data: Loaded
echo.
echo Next steps:
echo 1. Copy 'api' folder to C:\xampp\htdocs\forexclass\
echo 2. Start React frontend: cd frontend-react && npm install && npm start
echo 3. Access phpMyAdmin: http://localhost/phpmyadmin
echo.
pause
