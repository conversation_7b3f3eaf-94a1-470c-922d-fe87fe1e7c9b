import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react({
    // Use esbuild instead of babel
    jsxRuntime: 'automatic'
  })],
  server: {
    port: 5173,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost/forexclass',
        changeOrigin: true,
        secure: false
      }
    }
  },
  define: {
    'process.env': {}
  },
  esbuild: {
    // Force esbuild to handle JSX
    jsx: 'automatic'
  }
})
