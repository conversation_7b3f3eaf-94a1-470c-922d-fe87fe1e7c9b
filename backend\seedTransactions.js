const mongoose = require('mongoose');
const Transaction = require('./models/Transaction');
const User = require('./models/User');
const Package = require('./models/Package');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/forexclass')
  .then(() => {
    console.log('✅ Connected to MongoDB');
    seedTransactions();
  })
  .catch((error) => {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  });

async function seedTransactions() {
  try {
    console.log('🌱 Starting transaction seeding...');

    // Get existing users and packages
    const users = await User.find({ role: { $ne: 'admin' } }).limit(10);
    const packages = await Package.find({ isActive: true });

    if (users.length === 0) {
      console.log('⚠️ No users found. Please seed users first.');
      return;
    }

    if (packages.length === 0) {
      console.log('⚠️ No packages found. Please seed packages first.');
      return;
    }

    // Clear existing transactions
    await Transaction.deleteMany({});
    console.log('🗑️ Cleared existing transactions');

    // Sample transaction data
    const transactionStatuses = ['completed', 'pending', 'failed', 'processing'];
    const paymentMethods = ['mpesa', 'card'];
    const exchangeRate = 150; // USD to KSH

    const sampleTransactions = [];

    // Create 20 sample transactions
    for (let i = 0; i < 20; i++) {
      const user = users[Math.floor(Math.random() * users.length)];
      const pkg = packages[Math.floor(Math.random() * packages.length)];
      const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];
      const status = transactionStatuses[Math.floor(Math.random() * transactionStatuses.length)];
      
      // Create date within last 30 days
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - Math.floor(Math.random() * 30));

      const transaction = {
        userId: user._id,
        userEmail: user.email,
        userName: `${user.firstName} ${user.lastName}`,
        packageId: pkg._id,
        packageName: pkg.name,
        amount: {
          usd: pkg.price,
          ksh: pkg.price * exchangeRate
        },
        exchangeRate: exchangeRate,
        paymentMethod: paymentMethod,
        status: status,
        createdAt: createdAt
      };

      // Add payment method specific details
      if (paymentMethod === 'mpesa') {
        transaction.paymentDetails = {
          mpesaNumber: `+254${Math.floor(Math.random() * 900000000) + 700000000}`,
          mpesaCode: status === 'completed' ? `Q${Math.random().toString(36).substr(2, 8).toUpperCase()}` : null,
          mpesaReceiptNumber: status === 'completed' ? `R${Math.random().toString(36).substr(2, 10).toUpperCase()}` : null
        };
      } else {
        transaction.paymentDetails = {
          cardLast4: Math.floor(Math.random() * 9000) + 1000,
          cardBrand: ['Visa', 'Mastercard'][Math.floor(Math.random() * 2)],
          cardToken: `tok_${Math.random().toString(36).substr(2, 20)}`
        };
      }

      // Set processed date for completed transactions
      if (status === 'completed') {
        transaction.processedAt = new Date(createdAt.getTime() + Math.random() * 3600000); // Within 1 hour
        
        // Set subscription details
        transaction.subscriptionDetails = {
          startDate: transaction.processedAt,
          expiryDate: new Date(transaction.processedAt.getTime() + (pkg.duration * 24 * 60 * 60 * 1000)),
          duration: pkg.duration
        };
      }

      // Set failure reason for failed transactions
      if (status === 'failed') {
        const failureReasons = [
          'Insufficient funds',
          'Invalid card details',
          'Transaction timeout',
          'Payment declined by bank',
          'Network error'
        ];
        transaction.failureReason = failureReasons[Math.floor(Math.random() * failureReasons.length)];
      }

      sampleTransactions.push(transaction);
    }

    // Insert transactions
    const insertedTransactions = await Transaction.insertMany(sampleTransactions);
    console.log(`✅ Created ${insertedTransactions.length} sample transactions`);

    // Update user subscriptions for completed transactions
    const completedTransactions = insertedTransactions.filter(t => t.status === 'completed');
    
    for (const transaction of completedTransactions) {
      await User.findByIdAndUpdate(transaction.userId, {
        'subscription.plan': transaction.packageName,
        'subscription.status': 'active',
        'subscription.startDate': transaction.subscriptionDetails.startDate,
        'subscription.expiryDate': transaction.subscriptionDetails.expiryDate,
        'subscription.amount': `$${transaction.amount.usd}`,
        'subscription.paymentMethod': transaction.paymentMethod === 'mpesa' ? 'M-Pesa' : 'Card',
        'subscription.telegramAccess': true,
        'telegramStatus': 'added'
      });
    }

    console.log(`✅ Updated ${completedTransactions.length} user subscriptions`);

    // Display summary
    const stats = await Transaction.getStats();
    console.log('\n📊 Transaction Summary:');
    console.log(`Total Transactions: ${stats.totalTransactions}`);
    console.log(`Total Revenue: $${stats.totalRevenue}`);
    console.log(`Completed: ${stats.completedTransactions}`);
    console.log(`Pending: ${stats.pendingTransactions}`);
    console.log(`Failed: ${stats.failedTransactions}`);
    console.log(`M-Pesa: ${stats.mpesaTransactions}`);
    console.log(`Card: ${stats.cardTransactions}`);

    console.log('\n🎉 Transaction seeding completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('❌ Error seeding transactions:', error);
    process.exit(1);
  }
}
