const express = require('express');
const User = require('../models/User');
const Package = require('../models/Package');
const Transaction = require('../models/Transaction');
const auth = require('../middleware/auth');
const adminAuth = require('../middleware/adminAuth');
const router = express.Router();

// @route   GET /api/dashboard/stats
// @desc    Get dashboard statistics
// @access  Private/Admin
router.get('/stats', auth, adminAuth, async (req, res) => {
  try {
    // Get user statistics
    const totalUsers = await User.countDocuments({ role: { $ne: 'admin' } });
    const activeSubscriptions = await User.countDocuments({
      'subscription.status': 'active',
      role: { $ne: 'admin' }
    });
    const expiredSubscriptions = await User.countDocuments({
      'subscription.status': 'expired',
      role: { $ne: 'admin' }
    });
    const pendingSubscriptions = await User.countDocuments({
      'subscription.status': 'pending',
      role: { $ne: 'admin' }
    });

    // Get transaction statistics
    const transactionStats = await Transaction.getStats();
    
    // Get Telegram statistics
    const telegramMembers = await User.countDocuments({
      telegramStatus: 'added',
      role: { $ne: 'admin' }
    });
    const telegramPending = await User.countDocuments({
      telegramStatus: 'pending',
      role: { $ne: 'admin' }
    });

    // Calculate growth (last 30 days vs previous 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

    const recentUsers = await User.countDocuments({
      createdAt: { $gte: thirtyDaysAgo },
      role: { $ne: 'admin' }
    });
    const previousUsers = await User.countDocuments({
      createdAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo },
      role: { $ne: 'admin' }
    });

    const monthlyGrowth = previousUsers > 0 
      ? ((recentUsers - previousUsers) / previousUsers * 100).toFixed(1)
      : recentUsers > 0 ? 100 : 0;

    // Get package statistics
    const packageStats = await Package.aggregate([
      {
        $lookup: {
          from: 'users',
          let: { packageName: '$name' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$subscription.plan', '$$packageName'] },
                    { $eq: ['$subscription.status', 'active'] },
                    { $ne: ['$role', 'admin'] }
                  ]
                }
              }
            }
          ],
          as: 'subscribers'
        }
      },
      {
        $project: {
          name: 1,
          price: 1,
          subscriberCount: { $size: '$subscribers' }
        }
      }
    ]);

    const stats = {
      totalUsers,
      activeSubscriptions,
      expiredSubscriptions,
      pendingSubscriptions,
      totalRevenue: transactionStats.totalRevenue || 0,
      pendingTransactions: transactionStats.pendingTransactions || 0,
      completedTransactions: transactionStats.completedTransactions || 0,
      failedTransactions: transactionStats.failedTransactions || 0,
      telegramMembers,
      telegramPending,
      monthlyGrowth: parseFloat(monthlyGrowth),
      recentUsers,
      packageStats,
      transactionStats
    };

    res.json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/dashboard/recent-activity
// @desc    Get recent activity for dashboard
// @access  Private/Admin
router.get('/recent-activity', auth, adminAuth, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;

    // Get recent transactions
    const recentTransactions = await Transaction.find()
      .sort({ createdAt: -1 })
      .limit(limit)
      .populate('userId', 'firstName lastName email')
      .populate('packageId', 'name price');

    // Get recent user registrations
    const recentUsers = await User.find({ role: { $ne: 'admin' } })
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('firstName lastName email subscription createdAt');

    // Get recent subscription changes
    const recentSubscriptions = await User.find({
      'subscription.status': 'active',
      role: { $ne: 'admin' }
    })
      .sort({ 'subscription.startDate': -1 })
      .limit(limit)
      .select('firstName lastName email subscription');

    // Combine and format activities
    const activities = [];

    // Add transaction activities
    recentTransactions.forEach(transaction => {
      activities.push({
        type: 'transaction',
        title: `Payment ${transaction.status}`,
        description: `${transaction.userName} - ${transaction.packageName} (${transaction.formattedAmount.usd})`,
        timestamp: transaction.createdAt,
        status: transaction.status,
        icon: transaction.paymentMethod === 'mpesa' ? '📱' : '💳'
      });
    });

    // Add user registration activities
    recentUsers.forEach(user => {
      activities.push({
        type: 'registration',
        title: 'New user registered',
        description: `${user.fullName} - ${user.email}`,
        timestamp: user.createdAt,
        status: 'info',
        icon: '👤'
      });
    });

    // Add subscription activities
    recentSubscriptions.forEach(user => {
      activities.push({
        type: 'subscription',
        title: 'New subscription',
        description: `${user.fullName} - ${user.subscription.plan} Plan`,
        timestamp: user.subscription.startDate,
        status: 'success',
        icon: '🎯'
      });
    });

    // Sort by timestamp and limit
    activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    const limitedActivities = activities.slice(0, limit);

    res.json({
      success: true,
      data: { activities: limitedActivities }
    });

  } catch (error) {
    console.error('Recent activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/dashboard/transactions
// @desc    Get transactions for admin dashboard
// @access  Private/Admin
router.get('/transactions', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const status = req.query.status;
    const paymentMethod = req.query.paymentMethod;

    // Build query
    const query = {};
    if (status) query.status = status;
    if (paymentMethod) query.paymentMethod = paymentMethod;

    // Get transactions with pagination
    const transactions = await Transaction.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('userId', 'firstName lastName email')
      .populate('packageId', 'name price');

    // Get total count for pagination
    const total = await Transaction.countDocuments(query);

    // Format transactions for frontend
    const formattedTransactions = transactions.map(transaction => ({
      id: transaction.transactionId,
      _id: transaction._id,
      user: transaction.userName,
      email: transaction.userEmail,
      plan: transaction.packageName,
      amount: transaction.formattedAmount.usd,
      amountKSH: transaction.formattedAmount.ksh,
      paymentMethod: transaction.paymentMethod,
      mpesaNumber: transaction.paymentDetails.mpesaNumber,
      mpesaCode: transaction.paymentDetails.mpesaCode || 'N/A',
      cardLast4: transaction.paymentDetails.cardLast4,
      status: transaction.status,
      date: transaction.createdAt.toISOString(),
      processedAt: transaction.processedAt
    }));

    res.json({
      success: true,
      data: {
        transactions: formattedTransactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get transactions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
