const { sequelize, testConnection, syncDatabase } = require('./config/database');
require('dotenv').config();

console.log('🚀 Starting PostgreSQL Migration...');
console.log('=====================================');

async function migrateToPostgreSQL() {
  try {
    // Step 1: Test PostgreSQL connection
    console.log('📡 Testing PostgreSQL connection...');
    const connected = await testConnection();
    
    if (!connected) {
      console.log('❌ PostgreSQL connection failed!');
      console.log('');
      console.log('📋 Setup Instructions:');
      console.log('1. Install PostgreSQL on your system');
      console.log('2. Create a database named "forexclass_dev"');
      console.log('3. Update your .env file with correct database credentials:');
      console.log('   DB_HOST=localhost');
      console.log('   DB_PORT=5432');
      console.log('   DB_NAME=forexclass_dev');
      console.log('   DB_USER=postgres');
      console.log('   DB_PASSWORD=your_password');
      console.log('');
      console.log('🔧 Quick PostgreSQL Setup:');
      console.log('   # Install PostgreSQL (Windows)');
      console.log('   Download from: https://www.postgresql.org/download/windows/');
      console.log('');
      console.log('   # Create database');
      console.log('   psql -U postgres');
      console.log('   CREATE DATABASE forexclass_dev;');
      console.log('   CREATE DATABASE forexclass_test;');
      console.log('   \\q');
      console.log('');
      process.exit(1);
    }

    // Step 2: Create tables
    console.log('🏗️  Creating database tables...');
    await syncDatabase(false); // Don't force, preserve existing data if any

    console.log('✅ Database tables created successfully!');
    console.log('');

    // Step 3: Show next steps
    console.log('🎉 PostgreSQL Migration Complete!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('1. Update your .env file with PostgreSQL credentials');
    console.log('2. Run: npm run seed-data (to populate with sample data)');
    console.log('3. Start the server: npm run dev');
    console.log('');
    console.log('📊 Database Schema Created:');
    console.log('   ✅ users table');
    console.log('   ✅ packages table');
    console.log('   ✅ transactions table');
    console.log('');
    console.log('🔧 Available Commands:');
    console.log('   npm run migrate     - Run this migration');
    console.log('   npm run seed-data   - Populate with sample data');
    console.log('   npm run reset-db    - Reset database (careful!)');
    console.log('');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.log('');
    console.log('🔍 Common Issues:');
    console.log('1. PostgreSQL not installed or not running');
    console.log('2. Database credentials incorrect in .env file');
    console.log('3. Database "forexclass_dev" does not exist');
    console.log('4. User permissions insufficient');
    console.log('');
    console.log('💡 Try:');
    console.log('   - Check PostgreSQL service is running');
    console.log('   - Verify database exists: psql -U postgres -l');
    console.log('   - Test connection: psql -U postgres -d forexclass_dev');
    console.log('');
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run migration
migrateToPostgreSQL();
