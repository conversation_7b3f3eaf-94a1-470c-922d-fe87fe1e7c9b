const mongoose = require('mongoose');
const User = require('./models/User');
const Package = require('./models/Package');
const Transaction = require('./models/Transaction');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/forexclass')
  .then(() => {
    console.log('✅ Connected to MongoDB');
    initializeDatabase();
  })
  .catch((error) => {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  });

async function initializeDatabase() {
  try {
    console.log('🚀 Starting database initialization...');

    // Create indexes for better performance
    console.log('📊 Creating database indexes...');

    // Helper function to create index safely
    const createIndexSafely = async (collection, indexSpec, options = {}) => {
      try {
        await collection.createIndex(indexSpec, options);
        return true;
      } catch (error) {
        if (error.code === 86) { // IndexKeySpecsConflict
          console.log(`   ⚠️ Index already exists: ${JSON.stringify(indexSpec)}`);
          return false;
        }
        throw error;
      }
    };

    // User indexes
    let userIndexCount = 0;
    if (await createIndexSafely(User.collection, { email: 1 }, { unique: true })) userIndexCount++;
    if (await createIndexSafely(User.collection, { 'subscription.status': 1 })) userIndexCount++;
    if (await createIndexSafely(User.collection, { 'subscription.plan': 1 })) userIndexCount++;
    if (await createIndexSafely(User.collection, { telegramStatus: 1 })) userIndexCount++;
    if (await createIndexSafely(User.collection, { role: 1 })) userIndexCount++;
    if (await createIndexSafely(User.collection, { createdAt: -1 })) userIndexCount++;
    console.log(`✅ User indexes: ${userIndexCount} new indexes created`);

    // Package indexes
    let packageIndexCount = 0;
    if (await createIndexSafely(Package.collection, { name: 1 }, { unique: true })) packageIndexCount++;
    if (await createIndexSafely(Package.collection, { isActive: 1 })) packageIndexCount++;
    if (await createIndexSafely(Package.collection, { popular: 1 })) packageIndexCount++;
    if (await createIndexSafely(Package.collection, { priority: -1 })) packageIndexCount++;
    console.log(`✅ Package indexes: ${packageIndexCount} new indexes created`);

    // Transaction indexes
    let transactionIndexCount = 0;
    if (await createIndexSafely(Transaction.collection, { transactionId: 1 }, { unique: true })) transactionIndexCount++;
    if (await createIndexSafely(Transaction.collection, { userId: 1 })) transactionIndexCount++;
    if (await createIndexSafely(Transaction.collection, { packageId: 1 })) transactionIndexCount++;
    if (await createIndexSafely(Transaction.collection, { status: 1 })) transactionIndexCount++;
    if (await createIndexSafely(Transaction.collection, { paymentMethod: 1 })) transactionIndexCount++;
    if (await createIndexSafely(Transaction.collection, { createdAt: -1 })) transactionIndexCount++;
    if (await createIndexSafely(Transaction.collection, { 'paymentDetails.mpesaCode': 1 })) transactionIndexCount++;
    if (await createIndexSafely(Transaction.collection, { 'paymentDetails.mpesaReceiptNumber': 1 })) transactionIndexCount++;
    if (await createIndexSafely(Transaction.collection, { processedAt: -1 })) transactionIndexCount++;
    console.log(`✅ Transaction indexes: ${transactionIndexCount} new indexes created`);

    // Verify collections exist
    console.log('🔍 Verifying collections...');
    const collections = await mongoose.connection.db.listCollections().toArray();
    const collectionNames = collections.map(col => col.name);
    
    const requiredCollections = ['users', 'packages', 'transactions'];
    const missingCollections = requiredCollections.filter(name => !collectionNames.includes(name));
    
    if (missingCollections.length > 0) {
      console.log(`⚠️ Missing collections: ${missingCollections.join(', ')}`);
      
      // Create missing collections by inserting and removing a dummy document
      for (const collectionName of missingCollections) {
        if (collectionName === 'users') {
          const dummyUser = new User({
            firstName: 'Dummy',
            lastName: 'User',
            email: '<EMAIL>',
            password: 'temp123'
          });
          await dummyUser.save();
          await User.findByIdAndDelete(dummyUser._id);
        } else if (collectionName === 'packages') {
          const dummyPackage = new Package({
            name: 'Dummy Package',
            price: 1,
            duration: 1
          });
          await dummyPackage.save();
          await Package.findByIdAndDelete(dummyPackage._id);
        } else if (collectionName === 'transactions') {
          // Create a minimal transaction to initialize collection
          const users = await User.find().limit(1);
          const packages = await Package.find().limit(1);
          
          if (users.length > 0 && packages.length > 0) {
            const dummyTransaction = new Transaction({
              userId: users[0]._id,
              userEmail: users[0].email,
              userName: `${users[0].firstName} ${users[0].lastName}`,
              packageId: packages[0]._id,
              packageName: packages[0].name,
              amount: { usd: 1, ksh: 150 },
              exchangeRate: 150,
              paymentMethod: 'mpesa'
            });
            await dummyTransaction.save();
            await Transaction.findByIdAndDelete(dummyTransaction._id);
          }
        }
        console.log(`✅ Created collection: ${collectionName}`);
      }
    } else {
      console.log('✅ All required collections exist');
    }

    // Display collection statistics
    console.log('\n📈 Database Statistics:');
    
    const userCount = await User.countDocuments();
    const packageCount = await Package.countDocuments();
    const transactionCount = await Transaction.countDocuments();
    
    console.log(`👥 Users: ${userCount}`);
    console.log(`📦 Packages: ${packageCount}`);
    console.log(`💳 Transactions: ${transactionCount}`);

    // Display user statistics
    const adminCount = await User.countDocuments({ role: 'admin' });
    const activeSubscriptions = await User.countDocuments({ 'subscription.status': 'active' });
    const expiredSubscriptions = await User.countDocuments({ 'subscription.status': 'expired' });
    const pendingSubscriptions = await User.countDocuments({ 'subscription.status': 'pending' });
    
    console.log(`\n👤 User Breakdown:`);
    console.log(`   Admins: ${adminCount}`);
    console.log(`   Active Subscriptions: ${activeSubscriptions}`);
    console.log(`   Expired Subscriptions: ${expiredSubscriptions}`);
    console.log(`   Pending Subscriptions: ${pendingSubscriptions}`);

    // Display transaction statistics
    if (transactionCount > 0) {
      const transactionStats = await Transaction.getStats();
      console.log(`\n💰 Transaction Breakdown:`);
      console.log(`   Total Revenue: $${transactionStats.totalRevenue}`);
      console.log(`   Completed: ${transactionStats.completedTransactions}`);
      console.log(`   Pending: ${transactionStats.pendingTransactions}`);
      console.log(`   Failed: ${transactionStats.failedTransactions}`);
      console.log(`   M-Pesa: ${transactionStats.mpesaTransactions}`);
      console.log(`   Card: ${transactionStats.cardTransactions}`);
    }

    // Display package statistics
    if (packageCount > 0) {
      const activePackages = await Package.countDocuments({ isActive: true });
      const popularPackages = await Package.countDocuments({ popular: true });
      
      console.log(`\n📋 Package Breakdown:`);
      console.log(`   Active Packages: ${activePackages}`);
      console.log(`   Popular Packages: ${popularPackages}`);
    }

    // Verify indexes
    console.log('\n🔧 Verifying indexes...');
    const userIndexes = await User.collection.getIndexes();
    const packageIndexes = await Package.collection.getIndexes();
    const transactionIndexes = await Transaction.collection.getIndexes();
    
    console.log(`   User indexes: ${Object.keys(userIndexes).length}`);
    console.log(`   Package indexes: ${Object.keys(packageIndexes).length}`);
    console.log(`   Transaction indexes: ${Object.keys(transactionIndexes).length}`);

    console.log('\n🎉 Database initialization completed successfully!');
    console.log('\n📝 Ready for:');
    console.log('   ✅ User management and authentication');
    console.log('   ✅ Package/subscription management');
    console.log('   ✅ Transaction processing and tracking');
    console.log('   ✅ Dashboard analytics and reporting');
    console.log('   ✅ Payment integrations (M-Pesa, Card)');
    console.log('   ✅ Telegram bot integration');
    console.log('   ✅ Real-time activity tracking');

    process.exit(0);

  } catch (error) {
    console.error('❌ Error initializing database:', error);
    process.exit(1);
  }
}
