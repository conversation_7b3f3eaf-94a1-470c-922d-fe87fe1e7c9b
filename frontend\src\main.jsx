import React from 'react';
import { createRoot } from 'react-dom/client';

// Simple test component
function App() {
  const testAPI = async () => {
    try {
      const response = await fetch('http://localhost/forexclass/api/packages/index.php');
      const data = await response.json();
      alert(`API Test: ${data.success ? 'SUCCESS' : 'FAILED'}\nPackages: ${data.data?.packages?.length || 0}`);
    } catch (error) {
      alert(`API Error: ${error.message}`);
    }
  };

  return React.createElement('div', {
    style: { padding: '20px', fontFamily: 'Arial, sans-serif' }
  }, [
    React.createElement('h1', { key: 'title' }, 'ForexClass - React + Vite + PHP'),
    React.createElement('p', { key: 'desc' }, 'Your MySQL database and PHP API are working!'),
    React.createElement('button', {
      key: 'test',
      onClick: testAPI,
      style: {
        padding: '10px 20px',
        backgroundColor: '#007bff',
        color: 'white',
        border: 'none',
        borderRadius: '5px',
        cursor: 'pointer'
      }
    }, 'Test PHP API'),
    React.createElement('div', { key: 'info', style: { marginTop: '20px' } }, [
      React.createElement('h3', { key: 'h3' }, 'Backend Status: ✅ Working'),
      React.createElement('p', { key: 'p1' }, '• MySQL Database: ✅ Connected'),
      React.createElement('p', { key: 'p2' }, '• PHP API: ✅ Responding'),
      React.createElement('p', { key: 'p3' }, '• Login/Register: ✅ Ready'),
      React.createElement('p', { key: 'p4' }, '• Admin: <EMAIL> / admin123')
    ])
  ]);
}

const container = document.getElementById('root');
const root = createRoot(container);
root.render(React.createElement(App));
