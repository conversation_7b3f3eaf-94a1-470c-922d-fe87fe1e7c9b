-- ForexClass Database Schema for MySQL
-- Import this file into phpMyAdmin

CREATE DATABASE IF NOT EXISTS forexclass;
USE forexclass;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    telegram_username VARCHAR(50),
    telegram_status ENUM('pending', 'added', 'removed') DEFAULT 'pending',
    role ENUM('user', 'admin') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_telegram_status (telegram_status),
    INDEX idx_is_active (is_active)
);

-- User subscriptions table
CREATE TABLE user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    package_name VARCHAR(100),
    status ENUM('inactive', 'active', 'expired', 'cancelled') DEFAULT 'inactive',
    start_date DATE NULL,
    expiry_date DATE NULL,
    amount DECIMAL(10,2),
    payment_method ENUM('mpesa', 'card', 'bank') NULL,
    telegram_access BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_expiry_date (expiry_date)
);

-- Packages table
CREATE TABLE packages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration_days INT NOT NULL DEFAULT 30,
    features JSON,
    is_active BOOLEAN DEFAULT TRUE,
    is_popular BOOLEAN DEFAULT FALSE,
    color VARCHAR(20) DEFAULT 'blue',
    priority INT DEFAULT 0,
    telegram_access BOOLEAN DEFAULT FALSE,
    max_users INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_is_active (is_active),
    INDEX idx_is_popular (is_popular),
    INDEX idx_priority (priority)
);

-- Transactions table
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) NOT NULL UNIQUE,
    user_id INT NOT NULL,
    user_email VARCHAR(255) NOT NULL,
    user_name VARCHAR(100) NOT NULL,
    package_id INT NOT NULL,
    package_name VARCHAR(100) NOT NULL,
    amount_usd DECIMAL(10,2) NOT NULL,
    amount_ksh DECIMAL(10,2) NOT NULL,
    exchange_rate DECIMAL(10,4) DEFAULT 150.0000,
    payment_method ENUM('mpesa', 'card') NOT NULL,
    payment_details JSON,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    processed_at TIMESTAMP NULL,
    failure_reason TEXT NULL,
    admin_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE CASCADE,
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_user_id (user_id),
    INDEX idx_package_id (package_id),
    INDEX idx_status (status),
    INDEX idx_payment_method (payment_method),
    INDEX idx_created_at (created_at)
);

-- Insert sample packages
INSERT INTO packages (name, description, price, duration_days, features, is_active, is_popular, color, priority, telegram_access) VALUES
('Basic Trader', 'Perfect for beginners starting their trading journey', 29.99, 30, 
 JSON_ARRAY('Basic trading signals', 'Weekly market analysis', 'Email support', 'Trading basics course'), 
 TRUE, FALSE, 'blue', 1, TRUE),

('Pro Trader', 'Advanced tools and signals for serious traders', 79.99, 30, 
 JSON_ARRAY('Premium trading signals', 'Daily market analysis', 'Live trading sessions', 'Advanced strategies course', 'Priority support', 'Risk management tools'), 
 TRUE, TRUE, 'green', 2, TRUE),

('Elite Trader', 'Complete trading mastery with personal mentoring', 149.99, 30, 
 JSON_ARRAY('VIP trading signals', 'Real-time market alerts', 'One-on-one mentoring', 'Complete trading course', '24/7 priority support', 'Custom trading strategies', 'Portfolio management', 'Exclusive webinars'), 
 TRUE, FALSE, 'purple', 3, TRUE);

-- Insert admin user (password: admin123)
INSERT INTO users (first_name, last_name, email, password, phone, telegram_username, role, is_active) VALUES
('Admin', 'User', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJgusgdHu', '+254700000000', '@forexclassadmin', 'admin', TRUE);

-- Insert admin subscription
INSERT INTO user_subscriptions (user_id, package_name, status, start_date, expiry_date, amount, payment_method, telegram_access) VALUES
(1, 'Elite Trader', 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 365 DAY), 149.99, 'card', TRUE);

-- Insert sample users (password: password123)
INSERT INTO users (first_name, last_name, email, password, phone, telegram_username, role) VALUES
('John', 'Doe', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJgusgdHu', '+254700000001', '@johndoe', 'user'),
('Jane', 'Smith', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJgusgdHu', '+254700000002', '@janesmith', 'user'),
('Mike', 'Johnson', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJgusgdHu', '+254700000003', NULL, 'user');

-- Insert sample subscriptions
INSERT INTO user_subscriptions (user_id, package_name, status, start_date, expiry_date, amount, payment_method, telegram_access) VALUES
(2, 'Pro Trader', 'active', DATE_SUB(CURDATE(), INTERVAL 15 DAY), DATE_ADD(CURDATE(), INTERVAL 15 DAY), 79.99, 'mpesa', TRUE),
(3, 'Basic Trader', 'active', DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_ADD(CURDATE(), INTERVAL 20 DAY), 29.99, 'card', TRUE);

-- Insert sample transactions
INSERT INTO transactions (transaction_id, user_id, user_email, user_name, package_id, package_name, amount_usd, amount_ksh, payment_method, payment_details, status, processed_at) VALUES
('TXN' + UNIX_TIMESTAMP() + 'SEED1', 2, '<EMAIL>', 'John Doe', 2, 'Pro Trader', 79.99, 11998.50, 'mpesa', 
 JSON_OBJECT('mpesa_number', '+254700000001', 'mpesa_code', 'ABC123DEF', 'mpesa_receipt', 'QGH456IJK'), 
 'completed', DATE_SUB(NOW(), INTERVAL 15 DAY)),

('TXN' + UNIX_TIMESTAMP() + 'SEED2', 3, '<EMAIL>', 'Jane Smith', 1, 'Basic Trader', 29.99, 4498.50, 'card', 
 JSON_OBJECT('card_last4', '4242', 'card_brand', 'visa'), 
 'completed', DATE_SUB(NOW(), INTERVAL 10 DAY));
