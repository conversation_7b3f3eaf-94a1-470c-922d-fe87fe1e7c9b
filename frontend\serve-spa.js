const express = require('express');
const path = require('path');
const app = express();
const port = 3000;

// Serve static files from dist directory
app.use(express.static(path.join(__dirname, 'dist')));

// Handle React Router routes - send all requests to index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.listen(port, () => {
  console.log(`🚀 ForexClass website running at http://localhost:${port}`);
  console.log(`📱 Main site: http://localhost:${port}`);
  console.log(`🔐 Admin page: http://localhost:${port}/admin`);
  console.log(`📊 Admin dashboard: http://localhost:${port}/admin/dashboard`);
  console.log('');
  console.log('🔑 Admin Login: <EMAIL> / admin123');
  console.log('👤 User Login: <EMAIL> / password123');
  console.log('');
  console.log('⚠️  Make sure XAMPP (Apache + MySQL) is running for API to work!');
});
