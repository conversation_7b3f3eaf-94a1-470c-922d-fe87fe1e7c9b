const Transaction = require('../models/Transaction');
const User = require('../models/User');
const Package = require('../models/Package');

class TransactionService {
  /**
   * Create a new transaction
   * @param {Object} transactionData - Transaction data
   * @returns {Promise<Object>} Created transaction
   */
  static async createTransaction(transactionData) {
    try {
      const {
        userId,
        packageId,
        paymentMethod,
        paymentDetails,
        amount,
        exchangeRate,
        metadata = {}
      } = transactionData;

      // Get user and package details
      const user = await User.findById(userId);
      const pkg = await Package.findById(packageId);

      if (!user) {
        throw new Error('User not found');
      }

      if (!pkg) {
        throw new Error('Package not found');
      }

      // Create transaction
      const transaction = new Transaction({
        userId: user._id,
        userEmail: user.email,
        userName: `${user.firstName} ${user.lastName}`,
        packageId: pkg._id,
        packageName: pkg.name,
        amount: {
          usd: amount.usd || pkg.price,
          ksh: amount.ksh || (pkg.price * exchangeRate)
        },
        exchangeRate: exchangeRate || 150,
        paymentMethod,
        paymentDetails,
        subscriptionDetails: {
          duration: pkg.duration,
          startDate: null, // Will be set when payment is completed
          expiryDate: null
        },
        metadata,
        status: 'pending'
      });

      await transaction.save();
      return transaction;

    } catch (error) {
      throw new Error(`Failed to create transaction: ${error.message}`);
    }
  }

  /**
   * Update transaction status
   * @param {String} transactionId - Transaction ID
   * @param {String} status - New status
   * @param {Object} updateData - Additional data to update
   * @returns {Promise<Object>} Updated transaction
   */
  static async updateTransactionStatus(transactionId, status, updateData = {}) {
    try {
      const transaction = await Transaction.findOne({ transactionId });

      if (!transaction) {
        throw new Error('Transaction not found');
      }

      // Update status
      transaction.status = status;

      // Handle status-specific updates
      if (status === 'completed') {
        transaction.processedAt = new Date();
        
        // Set subscription dates
        const startDate = new Date();
        const expiryDate = new Date(startDate.getTime() + (transaction.subscriptionDetails.duration * 24 * 60 * 60 * 1000));
        
        transaction.subscriptionDetails.startDate = startDate;
        transaction.subscriptionDetails.expiryDate = expiryDate;

        // Update user subscription
        await User.findByIdAndUpdate(transaction.userId, {
          'subscription.plan': transaction.packageName,
          'subscription.status': 'active',
          'subscription.startDate': startDate,
          'subscription.expiryDate': expiryDate,
          'subscription.amount': `$${transaction.amount.usd}`,
          'subscription.paymentMethod': transaction.paymentMethod === 'mpesa' ? 'M-Pesa' : 'Card',
          'subscription.telegramAccess': true,
          'telegramStatus': 'pending' // Will be updated by Telegram service
        });

      } else if (status === 'failed') {
        transaction.failureReason = updateData.failureReason || 'Payment failed';
      } else if (status === 'refunded') {
        transaction.refundedAt = new Date();
        transaction.refundReason = updateData.refundReason || 'Refund requested';
        
        // Update user subscription to expired
        await User.findByIdAndUpdate(transaction.userId, {
          'subscription.status': 'expired',
          'subscription.telegramAccess': false,
          'telegramStatus': 'removed'
        });
      }

      // Apply additional updates
      Object.assign(transaction, updateData);

      await transaction.save();
      return transaction;

    } catch (error) {
      throw new Error(`Failed to update transaction: ${error.message}`);
    }
  }

  /**
   * Process M-Pesa payment
   * @param {String} transactionId - Transaction ID
   * @param {Object} mpesaData - M-Pesa callback data
   * @returns {Promise<Object>} Updated transaction
   */
  static async processMpesaPayment(transactionId, mpesaData) {
    try {
      const { mpesaCode, mpesaReceiptNumber, resultCode, resultDesc } = mpesaData;

      const updateData = {
        'paymentDetails.mpesaCode': mpesaCode,
        'paymentDetails.mpesaReceiptNumber': mpesaReceiptNumber
      };

      if (resultCode === '0') {
        // Success
        return await this.updateTransactionStatus(transactionId, 'completed', updateData);
      } else {
        // Failed
        updateData.failureReason = resultDesc || 'M-Pesa payment failed';
        return await this.updateTransactionStatus(transactionId, 'failed', updateData);
      }

    } catch (error) {
      throw new Error(`Failed to process M-Pesa payment: ${error.message}`);
    }
  }

  /**
   * Process card payment
   * @param {String} transactionId - Transaction ID
   * @param {Object} cardData - Card payment data
   * @returns {Promise<Object>} Updated transaction
   */
  static async processCardPayment(transactionId, cardData) {
    try {
      const { success, chargeId, failureReason } = cardData;

      const updateData = {
        'paymentDetails.cardToken': chargeId
      };

      if (success) {
        return await this.updateTransactionStatus(transactionId, 'completed', updateData);
      } else {
        updateData.failureReason = failureReason || 'Card payment failed';
        return await this.updateTransactionStatus(transactionId, 'failed', updateData);
      }

    } catch (error) {
      throw new Error(`Failed to process card payment: ${error.message}`);
    }
  }

  /**
   * Get transaction by ID
   * @param {String} transactionId - Transaction ID
   * @returns {Promise<Object>} Transaction
   */
  static async getTransaction(transactionId) {
    try {
      const transaction = await Transaction.findOne({ transactionId })
        .populate('userId', 'firstName lastName email')
        .populate('packageId', 'name price features');

      if (!transaction) {
        throw new Error('Transaction not found');
      }

      return transaction;

    } catch (error) {
      throw new Error(`Failed to get transaction: ${error.message}`);
    }
  }

  /**
   * Get user transactions
   * @param {String} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} User transactions
   */
  static async getUserTransactions(userId, options = {}) {
    try {
      const { page = 1, limit = 10, status } = options;
      
      const query = { userId };
      if (status) query.status = status;

      const transactions = await Transaction.find(query)
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .populate('packageId', 'name price features');

      const total = await Transaction.countDocuments(query);

      return {
        transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      throw new Error(`Failed to get user transactions: ${error.message}`);
    }
  }

  /**
   * Get transaction statistics
   * @param {Object} filters - Date and other filters
   * @returns {Promise<Object>} Transaction statistics
   */
  static async getTransactionStats(filters = {}) {
    try {
      const { startDate, endDate, paymentMethod, status } = filters;
      
      const matchStage = {};
      
      if (startDate || endDate) {
        matchStage.createdAt = {};
        if (startDate) matchStage.createdAt.$gte = new Date(startDate);
        if (endDate) matchStage.createdAt.$lte = new Date(endDate);
      }
      
      if (paymentMethod) matchStage.paymentMethod = paymentMethod;
      if (status) matchStage.status = status;

      const stats = await Transaction.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            totalTransactions: { $sum: 1 },
            totalRevenue: { $sum: '$amount.usd' },
            avgTransactionValue: { $avg: '$amount.usd' },
            completedTransactions: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
            },
            pendingTransactions: {
              $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
            },
            failedTransactions: {
              $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
            },
            mpesaTransactions: {
              $sum: { $cond: [{ $eq: ['$paymentMethod', 'mpesa'] }, 1, 0] }
            },
            cardTransactions: {
              $sum: { $cond: [{ $eq: ['$paymentMethod', 'card'] }, 1, 0] }
            }
          }
        }
      ]);

      return stats[0] || {
        totalTransactions: 0,
        totalRevenue: 0,
        avgTransactionValue: 0,
        completedTransactions: 0,
        pendingTransactions: 0,
        failedTransactions: 0,
        mpesaTransactions: 0,
        cardTransactions: 0
      };

    } catch (error) {
      throw new Error(`Failed to get transaction stats: ${error.message}`);
    }
  }

  /**
   * Expire pending transactions
   * @param {Number} hoursOld - Hours after which to expire transactions
   * @returns {Promise<Number>} Number of expired transactions
   */
  static async expirePendingTransactions(hoursOld = 24) {
    try {
      const cutoffDate = new Date(Date.now() - (hoursOld * 60 * 60 * 1000));
      
      const result = await Transaction.updateMany(
        {
          status: 'pending',
          createdAt: { $lt: cutoffDate }
        },
        {
          status: 'failed',
          failureReason: 'Transaction expired'
        }
      );

      return result.modifiedCount;

    } catch (error) {
      throw new Error(`Failed to expire transactions: ${error.message}`);
    }
  }
}

module.exports = TransactionService;
