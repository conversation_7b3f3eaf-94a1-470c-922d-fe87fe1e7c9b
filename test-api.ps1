# Test ForexClass API endpoints

Write-Host "🧪 Testing ForexClass API Endpoints" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

# Test 1: Packages API
Write-Host "📦 Testing Packages API..." -ForegroundColor Yellow
try {
    $packagesResponse = Invoke-RestMethod -Uri "http://localhost/forexclass/api/packages/index.php" -Method GET
    if ($packagesResponse.success) {
        Write-Host "✅ Packages API: SUCCESS" -ForegroundColor Green
        Write-Host "   Found $($packagesResponse.data.packages.Count) packages" -ForegroundColor Gray
    } else {
        Write-Host "❌ Packages API: FAILED" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Packages API: ERROR - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 2: Login API
Write-Host "🔐 Testing Login API..." -ForegroundColor Yellow
try {
    $loginData = @{
        email = "<EMAIL>"
        password = "admin123"
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "http://localhost/forexclass/api/auth/login.php" -Method POST -Body $loginData -ContentType "application/json"
    
    if ($loginResponse.success) {
        Write-Host "✅ Login API: SUCCESS" -ForegroundColor Green
        Write-Host "   User: $($loginResponse.user.first_name) $($loginResponse.user.last_name)" -ForegroundColor Gray
        Write-Host "   Role: $($loginResponse.user.role)" -ForegroundColor Gray
        Write-Host "   Token: $($loginResponse.token.Substring(0, 20))..." -ForegroundColor Gray
    } else {
        Write-Host "❌ Login API: FAILED - $($loginResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Login API: ERROR - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Database Connection
Write-Host "🗄️  Testing Database Connection..." -ForegroundColor Yellow
try {
    $dbTestResponse = Invoke-RestMethod -Uri "http://localhost/forexclass/api/config/database.php" -Method GET
    Write-Host "✅ Database: Connected" -ForegroundColor Green
} catch {
    Write-Host "❌ Database: Connection failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 API Testing Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Start React frontend: cd frontend-react && npm start" -ForegroundColor Gray
Write-Host "2. Access React app: http://localhost:3000" -ForegroundColor Gray
Write-Host "3. Login with: <EMAIL> / admin123" -ForegroundColor Gray
Write-Host ""
