# PostgreSQL Migration Guide

## Overview

This guide will help you migrate the ForexClass application from MongoDB to PostgreSQL. PostgreSQL offers better ACID compliance, complex queries, and robust transaction support.

## Prerequisites

### 1. Install PostgreSQL

#### Windows:
1. Download PostgreSQL from: https://www.postgresql.org/download/windows/
2. Run the installer and follow the setup wizard
3. Remember the password you set for the `postgres` user
4. Default port is 5432

#### macOS:
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql

# Create user
createuser -s postgres
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 2. Create Databases

Connect to PostgreSQL and create the required databases:

```sql
-- Connect to PostgreSQL
psql -U postgres

-- Create databases
CREATE DATABASE forexclass_dev;
CREATE DATABASE forexclass_test;

-- Create a dedicated user (optional but recommended)
CREATE USER forexclass_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE forexclass_dev TO forexclass_user;
GRANT ALL PRIVILEGES ON DATABASE forexclass_test TO forexclass_user;

-- Exit
\q
```

## Migration Steps

### Step 1: Update Dependencies

The required PostgreSQL dependencies have been added to `package.json`:
- `sequelize` - ORM for PostgreSQL
- `pg` - PostgreSQL client
- `pg-hstore` - Support for hstore data type

Install dependencies:
```bash
npm install
```

### Step 2: Environment Configuration

Update your `.env` file with PostgreSQL credentials:

```env
# PostgreSQL Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=forexclass_dev
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_SSL=false

# Test Database
DB_NAME_TEST=forexclass_test
```

### Step 3: Run Migration

Execute the migration script:

```bash
node migrate-to-postgresql.js
```

This will:
- Test PostgreSQL connection
- Create all necessary tables
- Set up indexes and constraints
- Provide feedback on success/failure

### Step 4: Verify Migration

Check that tables were created:

```sql
-- Connect to your database
psql -U postgres -d forexclass_dev

-- List tables
\dt

-- Check table structure
\d users
\d packages
\d transactions

-- Exit
\q
```

## Database Schema

### Users Table
- **Primary Key**: UUID
- **Fields**: firstName, lastName, email, password, phone, telegramUsername
- **JSON Fields**: subscription, profile, verification, metadata
- **Indexes**: email (unique), role, telegramStatus, subscription fields

### Packages Table
- **Primary Key**: UUID
- **Fields**: name, description, price, duration, isActive, popular
- **JSON Fields**: features, metadata, pricing
- **Indexes**: name (unique), isActive, popular, priority

### Transactions Table
- **Primary Key**: UUID
- **Fields**: userId, packageId, amount, paymentMethod, status
- **JSON Fields**: paymentDetails, subscriptionDetails, metadata
- **Foreign Keys**: userId → users.id, packageId → packages.id

## Key Differences from MongoDB

### 1. Data Types
- **MongoDB ObjectId** → **PostgreSQL UUID**
- **MongoDB subdocuments** → **PostgreSQL JSONB**
- **MongoDB arrays** → **PostgreSQL JSONB arrays**

### 2. Queries
- **MongoDB aggregation** → **PostgreSQL SQL with JSON functions**
- **MongoDB $lookup** → **PostgreSQL JOINs**
- **MongoDB $in** → **PostgreSQL IN or ANY**

### 3. Relationships
- **MongoDB references** → **PostgreSQL foreign keys**
- **MongoDB population** → **PostgreSQL includes/joins**

## Updated Code Examples

### User Model (Sequelize)
```javascript
const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  subscription: {
    type: DataTypes.JSONB,
    defaultValue: { status: 'inactive' }
  }
});
```

### Querying JSON Fields
```javascript
// Find users with active subscriptions
const activeUsers = await User.findAll({
  where: {
    'subscription.status': 'active'
  }
});

// Complex JSON queries
const premiumUsers = await User.findAll({
  where: sequelize.where(
    sequelize.fn('JSON_EXTRACT_PATH_TEXT', 
      sequelize.col('subscription'), 'plan'
    ), 'Premium'
  )
});
```

### Transactions with Relations
```javascript
// Get transaction with user and package details
const transaction = await Transaction.findByPk(id, {
  include: [
    { model: User, attributes: ['firstName', 'lastName', 'email'] },
    { model: Package, attributes: ['name', 'price'] }
  ]
});
```

## Performance Optimizations

### 1. Indexes
- Automatic indexes on foreign keys
- JSON field indexes using GIN
- Composite indexes for common queries

### 2. Connection Pooling
```javascript
const sequelize = new Sequelize(database, username, password, {
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
});
```

### 3. Query Optimization
- Use `attributes` to select specific fields
- Use `include` for eager loading
- Use `raw: true` for simple queries

## Troubleshooting

### Common Issues

#### 1. Connection Failed
```
Error: connect ECONNREFUSED 127.0.0.1:5432
```
**Solution**: Ensure PostgreSQL is running
```bash
# Windows
net start postgresql-x64-13

# macOS/Linux
sudo systemctl start postgresql
```

#### 2. Database Does Not Exist
```
Error: database "forexclass_dev" does not exist
```
**Solution**: Create the database
```sql
psql -U postgres
CREATE DATABASE forexclass_dev;
```

#### 3. Authentication Failed
```
Error: password authentication failed for user "postgres"
```
**Solution**: Check password in .env file or reset PostgreSQL password

#### 4. Permission Denied
```
Error: permission denied for database
```
**Solution**: Grant proper permissions
```sql
GRANT ALL PRIVILEGES ON DATABASE forexclass_dev TO your_user;
```

## Data Migration (Optional)

If you have existing MongoDB data to migrate:

### 1. Export MongoDB Data
```bash
mongoexport --db forexclass --collection users --out users.json
mongoexport --db forexclass --collection packages --out packages.json
mongoexport --db forexclass --collection transactions --out transactions.json
```

### 2. Transform and Import
Create a custom script to transform MongoDB documents to PostgreSQL format, handling:
- ObjectId → UUID conversion
- Date format standardization
- JSON structure adaptation

## Testing

### Run Tests
```bash
# Set test environment
NODE_ENV=test npm test

# Or run specific test files
NODE_ENV=test npm run test:models
NODE_ENV=test npm run test:api
```

### Manual Testing
```bash
# Start development server
npm run dev

# Test API endpoints
curl http://localhost:4001/api/health
curl http://localhost:4001/api/packages
```

## Production Deployment

### Environment Variables
```env
NODE_ENV=production
DB_HOST=your-production-host
DB_NAME=forexclass_prod
DB_USER=forexclass_user
DB_PASSWORD=secure_production_password
DB_SSL=true
```

### SSL Configuration
For production, enable SSL:
```javascript
dialectOptions: {
  ssl: {
    require: true,
    rejectUnauthorized: false
  }
}
```

## Rollback Plan

If you need to rollback to MongoDB:

1. Keep MongoDB models in `models/mongodb/` folder
2. Switch database configuration in environment
3. Update imports to use MongoDB models
4. Restart application

## Support

For issues with this migration:
1. Check PostgreSQL logs
2. Verify environment configuration
3. Test database connection manually
4. Review Sequelize documentation: https://sequelize.org/

## Next Steps

After successful migration:
1. Update all API routes to use Sequelize models
2. Test all functionality thoroughly
3. Update frontend if needed
4. Deploy to staging environment
5. Plan production migration
