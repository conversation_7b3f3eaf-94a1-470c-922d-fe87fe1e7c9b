const { Client } = require('pg');
require('dotenv').config();

async function testConnection() {
  console.log('🔍 Testing PostgreSQL Connection...');
  console.log('===================================');
  
  const users = [
    { user: 'postgres', passwords: ['12345', 'postgres', 'password', 'admin', '123456', 'root'] },
    { user: 'admin', passwords: ['12345', 'admin', 'password'] },
    { user: process.env.USERNAME, passwords: ['12345', 'password', ''] }
  ];

  const configs = [];

  // Try different user/password combinations
  for (const userConfig of users) {
    for (const pwd of userConfig.passwords) {
      configs.push({
        name: `User: "${userConfig.user}" Password: "${pwd}"`,
        host: 'localhost',
        port: 5432,
        database: 'postgres',
        user: userConfig.user,
        password: pwd
      });
    }
  }

  for (const config of configs) {
    console.log(`\n📡 Testing: ${config.name}`);
    console.log(`   Host: ${config.host}:${config.port}`);
    console.log(`   Database: ${config.database}`);
    console.log(`   User: ${config.user}`);
    
    const client = new Client(config);
    
    try {
      await client.connect();
      console.log('   ✅ Connection successful!');
      
      // Test query
      const result = await client.query('SELECT version()');
      console.log(`   📊 PostgreSQL Version: ${result.rows[0].version.split(' ')[0]} ${result.rows[0].version.split(' ')[1]}`);
      
      // List databases
      const dbResult = await client.query('SELECT datname FROM pg_database WHERE datistemplate = false');
      console.log(`   🗄️  Available databases: ${dbResult.rows.map(row => row.datname).join(', ')}`);
      
      await client.end();
      
      // If successful, update .env and break
      if (config.name !== 'Current Config') {
        console.log(`\n🔧 Updating .env with working configuration...`);
        // We found a working config, let's use it
        return config;
      }
      return config;
      
    } catch (error) {
      console.log(`   ❌ Connection failed: ${error.message}`);
      try {
        await client.end();
      } catch (e) {
        // Ignore cleanup errors
      }
    }
  }
  
  console.log('\n❌ All connection attempts failed!');
  console.log('\n🔧 Troubleshooting:');
  console.log('1. Check if PostgreSQL service is running');
  console.log('2. Verify the password is correct');
  console.log('3. Check if PostgreSQL is listening on port 5432');
  console.log('4. Try connecting with pgAdmin or another PostgreSQL client');
  
  return null;
}

testConnection().then(workingConfig => {
  if (workingConfig) {
    console.log('\n🎉 Connection test completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Create the forexclass_dev database');
    console.log('2. Run the migration script');
  }
}).catch(error => {
  console.error('Test failed:', error);
});
