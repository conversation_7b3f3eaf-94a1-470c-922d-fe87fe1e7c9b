const mongoose = require('mongoose');

const packageSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Package name is required'],
    trim: true,
    maxlength: [100, 'Package name cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative']
  },
  duration: {
    type: Number,
    required: [true, 'Duration is required'],
    min: [1, 'Duration must be at least 1 day'],
    default: 30
  },
  features: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  popular: {
    type: Boolean,
    default: false
  },
  color: {
    type: String,
    enum: ['blue', 'green', 'purple', 'red', 'yellow', 'indigo'],
    default: 'blue'
  },
  maxUsers: {
    type: Number,
    default: null // null means unlimited
  },
  telegramAccess: {
    type: <PERSON>olean,
    default: false
  },
  priority: {
    type: Number,
    default: 0 // Higher number = higher priority in display
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for subscriber count
packageSchema.virtual('subscribers', {
  ref: 'User',
  localField: '_id',
  foreignField: 'subscription.packageId',
  count: true
});

// Index for better query performance
packageSchema.index({ name: 1 });
packageSchema.index({ isActive: 1 });
packageSchema.index({ priority: -1 });

// Pre-save middleware to ensure only one package is marked as popular at a time
packageSchema.pre('save', async function(next) {
  if (this.popular && this.isModified('popular')) {
    // Remove popular flag from other packages
    await this.constructor.updateMany(
      { _id: { $ne: this._id } },
      { popular: false }
    );
  }
  next();
});

// Method to get active packages
packageSchema.statics.getActivePackages = function() {
  return this.find({ isActive: true }).sort({ priority: -1, createdAt: 1 });
};

// Method to get package with subscriber count
packageSchema.statics.getPackageWithStats = function(packageId) {
  return this.findById(packageId).populate('subscribers');
};

const Package = mongoose.model('Package', packageSchema);

module.exports = Package;
