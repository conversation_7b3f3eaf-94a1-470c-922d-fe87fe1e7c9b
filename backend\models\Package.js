const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Package = sequelize.define('Package', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      len: [2, 100]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  duration: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 30,
    validate: {
      min: 1
    }
  },
  features: {
    type: DataTypes.JSONB,
    defaultValue: []
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  popular: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  color: {
    type: DataTypes.STRING(20),
    defaultValue: 'blue',
    validate: {
      isIn: [['blue', 'green', 'purple', 'red', 'yellow', 'gray', 'indigo', 'pink']]
    }
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  telegramAccess: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  maxUsers: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    }
  },
  // Package metadata
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {
      category: 'trading',
      level: 'beginner',
      tags: []
    }
  }
}, {
  tableName: 'packages',
  timestamps: true
});

// Instance methods
Package.prototype.getFormattedPrice = function() {
  return `$${parseFloat(this.price).toFixed(2)}`;
};

Package.prototype.isAvailable = function() {
  return this.isActive;
};

Package.prototype.getDurationText = function() {
  if (this.duration === 1) return '1 day';
  if (this.duration === 7) return '1 week';
  if (this.duration === 30) return '1 month';
  if (this.duration === 365) return '1 year';
  return `${this.duration} days`;
};

// Class methods
Package.getActivePackages = function() {
  return this.findAll({
    where: { isActive: true },
    order: [['priority', 'ASC'], ['price', 'ASC']]
  });
};

Package.getPopularPackages = function() {
  return this.findAll({
    where: { 
      isActive: true,
      popular: true 
    },
    order: [['priority', 'ASC']]
  });
};

module.exports = Package;
