<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test ForexClass PHP Backend</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; width: 200px; }
    </style>
</head>
<body>
    <h1>🧪 ForexClass PHP Backend Test</h1>
    <p>Test your MySQL database and PHP API endpoints</p>

    <!-- Test 1: Packages API -->
    <div class="test-section" id="packages-test">
        <h3>📦 Test 1: Packages API</h3>
        <button onclick="testPackages()">Test Packages Endpoint</button>
        <div id="packages-result"></div>
    </div>

    <!-- Test 2: Login API -->
    <div class="test-section" id="login-test">
        <h3>🔐 Test 2: Login API</h3>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="admin123">
        <button onclick="testLogin()">Test Login</button>
        <div id="login-result"></div>
    </div>

    <!-- Test 3: Register API -->
    <div class="test-section" id="register-test">
        <h3>👤 Test 3: Register API</h3>
        <input type="text" id="firstName" placeholder="First Name" value="Test">
        <input type="text" id="lastName" placeholder="Last Name" value="User">
        <input type="email" id="regEmail" placeholder="Email" value="<EMAIL>">
        <input type="password" id="regPassword" placeholder="Password" value="password123">
        <button onclick="testRegister()">Test Register</button>
        <div id="register-result"></div>
    </div>

    <!-- Database Info -->
    <div class="test-section">
        <h3>🗄️ Database Info</h3>
        <p><strong>Database:</strong> forexclass (MySQL)</p>
        <p><strong>phpMyAdmin:</strong> <a href="http://localhost/phpmyadmin" target="_blank">http://localhost/phpmyadmin</a></p>
        <p><strong>Tables:</strong> users, packages, user_subscriptions, transactions</p>
    </div>

    <script>
        const API_BASE = 'http://localhost/forexclass/api';

        async function testPackages() {
            const resultDiv = document.getElementById('packages-result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/packages/index.php`);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Success!</h4>
                            <p>Found ${data.data.packages.length} packages</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Failed</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            resultDiv.innerHTML = '<p>Testing login...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/auth/login.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Login Success!</h4>
                            <p>Welcome ${data.user.first_name} ${data.user.last_name}</p>
                            <p>Role: ${data.user.role}</p>
                            <p>Token: ${data.token.substring(0, 20)}...</p>
                            <pre>${JSON.stringify(data.user, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Login Failed</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testRegister() {
            const resultDiv = document.getElementById('register-result');
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            
            resultDiv.innerHTML = '<p>Testing registration...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/auth/register.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ firstName, lastName, email, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Registration Success!</h4>
                            <p>User created: ${data.user.first_name} ${data.user.last_name}</p>
                            <p>Email: ${data.user.email}</p>
                            <p>Token: ${data.token.substring(0, 20)}...</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Registration Failed</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Auto-test packages on load
        window.onload = function() {
            testPackages();
        };
    </script>
</body>
</html>
