# Server Configuration
NODE_ENV=development
PORT=5000

# Frontend URL
FRONTEND_URL=http://localhost:5173

# PostgreSQL Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=forexclass_dev
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false

# Test Database
DB_NAME_TEST=forexclass_test

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Email Configuration (for future use)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# M-Pesa Configuration (for future use)
MPESA_CONSUMER_KEY=your-mpesa-consumer-key
MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
MPESA_BUSINESS_SHORT_CODE=174379
MPESA_PASSKEY=your-mpesa-passkey
MPESA_ENVIRONMENT=sandbox

# SMS Configuration (for future use)
SMS_API_KEY=your-sms-api-key
SMS_USERNAME=your-sms-username
SMS_SENDER_ID=ForexClass

# Telegram Configuration (for future use)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_CHANNEL_ID=your-telegram-channel-id
