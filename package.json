{"name": "forexclass", "version": "1.0.0", "description": "A full-stack web application offering crypto and forex courses with premium Telegram signal access.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Danchri/forex.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Danchri/forex/issues"}, "homepage": "https://github.com/Danchri/forex#readme", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.4"}, "devDependencies": {"nodemon": "^3.1.10"}}