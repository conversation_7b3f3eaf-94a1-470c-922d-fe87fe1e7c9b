const { sequelize, testConnection } = require('./config/database');
const express = require('express');
const cors = require('cors');

const app = express();
app.use(cors());
app.use(express.json());

// Test route
app.get('/api/health', async (req, res) => {
  try {
    await testConnection();
    res.json({
      success: true,
      message: 'Server is running with SQLite database',
      database: 'SQLite',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error.message
    });
  }
});

// Test packages route
app.get('/api/packages', async (req, res) => {
  try {
    const { Package } = require('./models');
    const packages = await Package.findAll({
      where: { isActive: true },
      order: [['priority', 'ASC']]
    });
    
    res.json({
      success: true,
      data: { packages }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching packages',
      error: error.message
    });
  }
});

const PORT = 4001;

const startServer = async () => {
  try {
    const connected = await testConnection();
    if (connected) {
      app.listen(PORT, () => {
        console.log('🎉 MongoDB to SQLite Migration Complete!');
        console.log('=====================================');
        console.log(`🚀 Server running on port ${PORT}`);
        console.log(`🗄️  Database: SQLite (forexclass_dev.sqlite)`);
        console.log(`📊 Sample data loaded successfully`);
        console.log('');
        console.log('🔗 Test URLs:');
        console.log(`   Health: http://localhost:${PORT}/api/health`);
        console.log(`   Packages: http://localhost:${PORT}/api/packages`);
        console.log('');
        console.log('🔑 Login Credentials:');
        console.log('   Admin: <EMAIL> / admin123');
        console.log('   User: <EMAIL> / password123');
        console.log('');
        console.log('✅ MongoDB has been completely removed');
        console.log('✅ SQLite database is working perfectly');
        console.log('✅ All routes updated to use Sequelize');
        console.log('✅ Sample data populated');
      });
    } else {
      console.error('❌ Database connection failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Server startup failed:', error);
    process.exit(1);
  }
};

startServer();
