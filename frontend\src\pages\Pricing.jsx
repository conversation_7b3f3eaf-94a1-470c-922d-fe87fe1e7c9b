import React, { useState, useEffect } from 'react'
import apiService from '../services/api'
import { useCurrency } from '../hooks/useCurrency'

const Pricing = () => {
  const [plans, setPlans] = useState([])
  const [isLoadingPlans, setIsLoadingPlans] = useState(true)
  const [plansError, setPlansError] = useState('')
  const { formatUSDToKSH, isLoading: currencyLoading } = useCurrency()

  // Fetch packages from API
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setIsLoadingPlans(true)
        setPlansError('')
        const response = await apiService.getPackages()

        if (response.success) {
          // Transform API data to match frontend format
          const transformedPlans = response.data.packages
            .filter(pkg => pkg.isActive) // Only show active packages
            .sort((a, b) => (a.priority || 0) - (b.priority || 0)) // Sort by priority
            .map(pkg => ({
              name: pkg.name,
              price: `$${pkg.price}`,
              period: "/month",
              description: pkg.description || `Perfect for ${pkg.name.toLowerCase()} traders`,
              features: pkg.features || [],
              popular: pkg.popular || false,
              color: pkg.color || 'gray',
              originalPrice: pkg.price
            }))

          setPlans(transformedPlans)
        } else {
          setPlansError('Failed to load pricing plans')
          setPlans(getDefaultPlans())
        }
      } catch (error) {
        console.error('Error fetching plans:', error)
        setPlansError('Error loading pricing plans')
        setPlans(getDefaultPlans())
      } finally {
        setIsLoadingPlans(false)
      }
    }

    fetchPlans()
  }, [])

  // Fallback default plans
  const getDefaultPlans = () => [
    {
      name: "Basic",
      price: "$29",
      period: "/month",
      features: [
        "Access to basic courses",
        "Community forum access",
        "Email support",
        "Basic trading guides"
      ],
      popular: false,
      originalPrice: 29
    },
    {
      name: "Premium",
      price: "$99",
      period: "/month",
      features: [
        "Access to all courses",
        "Premium Telegram signals",
        "1-on-1 mentoring sessions",
        "Advanced trading strategies",
        "Priority support",
        "Market analysis reports"
      ],
      popular: true,
      originalPrice: 99
    },
    {
      name: "VIP",
      price: "$199",
      period: "/month",
      features: [
        "Everything in Premium",
        "Personal trading coach",
        "Custom trading strategies",
        "Direct access to expert traders",
        "Weekly live sessions",
        "Portfolio review"
      ],
      popular: false,
      originalPrice: 199
    }
  ]

  return (
    <div className="bg-gray-50 min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
          <p className="text-lg text-gray-600">Select the perfect plan for your trading journey</p>
        </div>

        {/* Loading State */}
        {isLoadingPlans ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading pricing plans...</span>
          </div>
        ) : plansError ? (
          <div className="text-center py-12">
            <div className="text-red-500 mb-4">⚠️ {plansError}</div>
            <p className="text-gray-600 mb-4">Using default pricing plans</p>
          </div>
        ) : null}

        <div className="grid md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <div key={index} className={`card relative ${plan.popular ? 'ring-2 ring-blue-500' : ''}`}>
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}
              
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                {plan.description && (
                  <p className="text-gray-600 mb-4 text-sm">{plan.description}</p>
                )}

                {/* Pricing Display */}
                <div className="mb-4">
                  {/* USD Price (smaller, crossed out) */}
                  <div className="flex items-center justify-center mb-1">
                    <span className="text-lg text-gray-400 line-through">{plan.price}</span>
                    <span className="text-gray-400 ml-1 text-sm">{plan.period}</span>
                  </div>

                  {/* KSH Price (prominent) */}
                  <div className="flex items-center justify-center">
                    <span className="text-3xl font-bold text-blue-600">
                      {currencyLoading ? '...' : formatUSDToKSH(plan.originalPrice || plan.price)}
                    </span>
                    <span className="text-gray-500 ml-2 text-base">{plan.period}</span>
                  </div>

                  {/* Currency info */}
                  <div className="text-xs text-gray-500 mt-1">
                    Pay in Kenyan Shillings via M-Pesa
                  </div>
                </div>
              </div>

              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>

              <button className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                plan.popular 
                  ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
              }`}>
                Get Started
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Pricing
