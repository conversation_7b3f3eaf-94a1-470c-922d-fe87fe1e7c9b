const { sequelize, testConnection } = require('./config/database');
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// Import routes
const authRoutes = require('./routes/auth');
const packagesRoutes = require('./routes/packages');
const usersRoutes = require('./routes/users');
const dashboardRoutes = require('./routes/dashboard');
const transactionsRoutes = require('./routes/transactions');

const app = express();

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.'
  }
});
app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check route
app.get('/api/health', async (req, res) => {
  try {
    await testConnection();
    res.json({
      success: true,
      message: 'Server is running with SQLite database',
      database: 'SQLite',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error.message
    });
  }
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/packages', packagesRoutes);
app.use('/api/users', usersRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/transactions', transactionsRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

const PORT = process.env.PORT || 4000;

const startServer = async () => {
  try {
    console.log('🎉 MongoDB to SQLite Migration Complete!');
    console.log('=====================================');
    
    const connected = await testConnection();
    if (connected) {
      await sequelize.sync({ alter: true });
      console.log('✅ Connected to SQLite and models synchronized');
      
      app.listen(PORT, () => {
        console.log(`🚀 Server running on port ${PORT}`);
        console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
        console.log(`🌐 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
        console.log(`🗄️  Database: SQLite (forexclass_dev.sqlite)`);
        console.log('');
        console.log('🔗 API Endpoints:');
        console.log(`   Health: http://localhost:${PORT}/api/health`);
        console.log(`   Packages: http://localhost:${PORT}/api/packages`);
        console.log(`   Auth: http://localhost:${PORT}/api/auth/login`);
        console.log('');
        console.log('🔑 Login Credentials:');
        console.log('   Admin: <EMAIL> / admin123');
        console.log('   User: <EMAIL> / password123');
        console.log('');
        console.log('✅ MongoDB has been completely removed');
        console.log('✅ SQLite database is working perfectly');
        console.log('✅ All routes updated to use Sequelize');
        console.log('✅ Sample data populated');
        console.log('');
        console.log('🎯 Ready for frontend connection!');
      });
    } else {
      console.error('❌ Database connection failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Server startup failed:', error);
    process.exit(1);
  }
};

startServer();
