<?php
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../config/database.php';

// Initialize database
$database = new Database();
$db = $database->getConnection();

// Prepare query to get active packages
$query = "SELECT 
            p.id, p.name, p.description, p.price, p.duration_days, 
            p.features, p.is_active, p.is_popular, p.color, p.priority, 
            p.telegram_access, p.max_users, p.created_at,
            COUNT(us.id) as subscriber_count
          FROM packages p 
          LEFT JOIN user_subscriptions us ON p.name = us.package_name AND us.status = 'active'
          WHERE p.is_active = 1 
          GROUP BY p.id
          ORDER BY p.priority ASC, p.created_at ASC";

$stmt = $db->prepare($query);
$stmt->execute();

$packages = array();

while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    // Decode JSON features
    $features = json_decode($row['features'], true);
    if (!$features) {
        $features = [];
    }
    
    // Format package data
    $package = array(
        "id" => (int)$row['id'],
        "name" => $row['name'],
        "description" => $row['description'],
        "price" => (float)$row['price'],
        "duration" => (int)$row['duration_days'],
        "features" => $features,
        "isActive" => (bool)$row['is_active'],
        "popular" => (bool)$row['is_popular'],
        "color" => $row['color'],
        "priority" => (int)$row['priority'],
        "telegramAccess" => (bool)$row['telegram_access'],
        "maxUsers" => $row['max_users'] ? (int)$row['max_users'] : null,
        "subscribers" => (int)$row['subscriber_count'],
        "createdAt" => $row['created_at']
    );
    
    array_push($packages, $package);
}

// Return packages
http_response_code(200);
echo json_encode([
    "success" => true,
    "data" => [
        "packages" => $packages
    ]
]);
?>
