<?php
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../config/database.php';

// Get posted data
$data = json_decode(file_get_contents("php://input"));

// Check if data is valid
if (!empty($data->email) && !empty($data->password)) {
    
    // Initialize database
    $database = new Database();
    $db = $database->getConnection();
    
    // Prepare query
    $query = "SELECT 
                u.id, u.first_name, u.last_name, u.email, u.password, u.phone, 
                u.telegram_username, u.telegram_status, u.role, u.is_active, u.last_login,
                us.package_name, us.status as subscription_status, us.start_date, 
                us.expiry_date, us.telegram_access
              FROM users u 
              LEFT JOIN user_subscriptions us ON u.id = us.user_id 
              WHERE u.email = :email AND u.is_active = 1 
              LIMIT 1";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(":email", $data->email);
    $stmt->execute();
    
    $num = $stmt->rowCount();
    
    if ($num > 0) {
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Verify password
        if (password_verify($data->password, $row['password'])) {
            
            // Update last login
            $update_query = "UPDATE users SET last_login = NOW() WHERE id = :id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->bindParam(":id", $row['id']);
            $update_stmt->execute();
            
            // Generate JWT token (simplified version)
            $token = base64_encode(json_encode([
                'user_id' => $row['id'],
                'email' => $row['email'],
                'role' => $row['role'],
                'exp' => time() + (7 * 24 * 60 * 60) // 7 days
            ]));
            
            // Prepare user data (remove password)
            unset($row['password']);
            
            // Format subscription data
            $subscription = null;
            if ($row['package_name']) {
                $subscription = [
                    'plan' => $row['package_name'],
                    'status' => $row['subscription_status'],
                    'startDate' => $row['start_date'],
                    'expiryDate' => $row['expiry_date'],
                    'telegramAccess' => (bool)$row['telegram_access']
                ];
            }
            
            // Remove subscription fields from main user object
            unset($row['package_name'], $row['subscription_status'], $row['start_date'], $row['expiry_date'], $row['telegram_access']);
            
            // Add subscription to user object
            $row['subscription'] = $subscription;
            
            // Return success response
            http_response_code(200);
            echo json_encode([
                "success" => true,
                "message" => "Login successful",
                "token" => $token,
                "user" => $row
            ]);
            
        } else {
            // Invalid password
            http_response_code(401);
            echo json_encode([
                "success" => false,
                "message" => "Invalid email or password"
            ]);
        }
    } else {
        // User not found
        http_response_code(401);
        echo json_encode([
            "success" => false,
            "message" => "Invalid email or password"
        ]);
    }
} else {
    // Missing data
    http_response_code(400);
    echo json_encode([
        "success" => false,
        "message" => "Email and password are required"
    ]);
}
?>
